import request from '@/utils/request'

export function getNovelList(data) {
  return request({
    url: '/novel/getNovelList',
    method: 'post',
    data
  })
}

export function getNovelInfo(data) {
  return request({
    url: '/novel/getNovelInfo',
    method: 'post',
    data
  })
}

export function saveNovelInfo(data) {
  return request({
    url: '/novel/saveNovelInfo',
    method: 'post',
    data
  })
}

export function delNovel(data) {
  return request({
    url: '/novel/delNovel',
    method: 'post',
    data
  })
}

export function getTaskList(data) {
  return request({
    url: '/novel/getTaskList',
    method: 'post',
    data
  })
}
export function delTask(data) {
  return request({
    url: '/novel/delTask',
    method: 'post',
    data
  })
}
export function saveTask(data) {
  return request({
    url: '/novel/saveTask',
    method: 'post',
    data
  })
}
export function delAllTask(data) {
  return request({
    url: '/novel/delAllTask',
    method: 'post',
    data
  })
}
