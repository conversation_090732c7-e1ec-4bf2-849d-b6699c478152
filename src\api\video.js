import request from '@/utils/request'

export function submitTask(data) {
  return request({
    url: '/video/submitTask',
    method: 'post',
    data
  })
}

export function getVideoSetting(data) {
  return request({
    url: '/video/getVideoSetting',
    method: 'post',
    data
  })
}

export function getVideoOptions(data) {
  return request({
    url: '/video/getVideoOptions',
    method: 'post',
    data
  })
}

export function getHistoryMsg(data) {
  return request({
    url: '/video/getHistoryMsg',
    method: 'post',
    data
  })
}

export function getPublicList(data) {
  return request({
    url: '/video/getPublicList',
    method: 'post',
    data
  })
}

export function getVideoResult(data) {
  return request({
    url: '/video/getVideoResult',
    method: 'post',
    hideLoading: true,
    data
  })
}

export function uploadMedia(data) {
  return request({
    url: '/video/uploadMedia',
    method: 'post',
    data
  })
}

export function upscaleVideo(data) {
  return request({
    url: '/video/upscale',
    method: 'post',
    data
  })
}
