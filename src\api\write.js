import request from '@/utils/request'

export function getAllPrompt(data) {
  return request({
    url: '/write/getAllPrompt',
    method: 'post',
    data
  })
}
export function votePrompt(data) {
  return request({
    url: '/write/votePrompt',
    method: 'post',
    data
  })
}
export function getPrompt(data) {
  return request({
    url: '/write/getPrompt',
    method: 'post',
    data
  })
}
export function getWriteHistoryMsg(data) {
  return request({
    url: '/write/getHistoryMsg',
    method: 'post',
    data
  })
}

/**
 * 搜索创作模型
 * @param {Object} params - 搜索参数
 * @param {string} [params.keyword=''] - 搜索关键词，支持标题和描述搜索
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pagesize=10] - 每页数量
 * @param {string} [params.topic_id='all'] - 主题ID，'all'表示所有主题
 * @param {string} [params.topic_two_id=''] - 二级主题ID
 * @returns {Promise<Object>} 搜索结果
 * @example
 * // 基础搜索
 * const result = await getPrompts({ keyword: 'AI', page: 1, pagesize: 10 })
 * 
 * // 按主题搜索
 * const result = await getPrompts({ keyword: '写作', topic_id: '5' })
 */
export function getPrompts(params) {
  return request({
    url: '/write/getPrompts',
    method: 'get',
    params
  })
}

/**
 * 获取用户收藏的创作模型列表
 * @param {Object} params - 请求参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pagesize=10] - 每页数量
 * @returns {Promise<Object>} 收藏列表结果
 * @example
 * // 获取第一页收藏列表
 * const result = await getVotePrompts({ page: 1, pagesize: 20 })
 */
export function getVotePrompts(params) {
  return request({
    url: '/write/getVotePrompts',
    method: 'get',
    params
  })
}
