import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)
import Layout from '@/views/index/index'

export const staticRoutes = [
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '',
        name: 'chat',
        meta: { title: '对话' }
      },
      {
        path: 'write',
        name: 'write',
        meta: { title: '文本创作' }
      },
      {
        path: 'cosplay',
        name: 'cosplay',
        meta: { title: '角色模拟' }
      },
      {
        path: 'draw',
        name: 'draw',
        meta: { title: 'AI绘画' }
      },
      {
        path: 'video',
        name: 'video',
        meta: { title: 'AI视频' }
      },
      {
        path: 'music',
        name: 'music',
        meta: { title: 'AI音乐' }
      },
      {
        path: 'pk',
        name: 'pk',
        meta: { title: 'AI擂台' }
      },
      {
        path: 'batch',
        name: 'batch',
        meta: { title: '批量生成' }
      },
      {
        path: 'novel',
        name: 'novel',
        meta: { title: '长篇写作' }
      },
      {
        path: 'team',
        name: 'team',
        meta: { title: 'AI团队' }
      },
      {
        path: 'mind',
        name: 'mind',
        meta: { title: '思维导图' }
      },
      {
        path: 'apps',
        name: 'apps',
        meta: { title: '应用中心' }
      }
    ],
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },
  {
    path: '/doc',
    name: 'Doc',
    component: () => import('@/views/doc'),
    meta: { title: '文档中心' },
    hidden: true
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]
export const asyncRoutes = []

const createRouter = () => new Router({
  mode: 'hash', // require service support
  base: '/web/',
  scrollBehavior: () => ({ y: 0 }),
  routes: staticRoutes
})
const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
