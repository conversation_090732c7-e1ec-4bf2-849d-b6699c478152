.module-music {
  .music-setting {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    border-radius: 10px;
    overflow: hidden;
    transition: background-color 0.1s ease-in-out;

    .module-body {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 80px;
      padding: 10px 0;

      .scrollbar-wrapper {
        overflow-x: hidden !important;
      }

      .el-scrollbar__bar.is-vertical {
        right: 0;
        width: 3px;
      }

      .el-scrollbar {
        height: 100%;
      }

      .box-music-setting {
        padding: 10px 20px;
        box-sizing: border-box;
        //transform: scale(0.88);
        .setting-row {
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;
          .setting-item {
            width: 100%;
            padding: 10px 20px 20px 20px;
            background: #eff0f0;
            border-radius: 10px;
            &.col-1 {
              width: 220px;
            }
            &.col-2 {
              width: 350px;
            }
            &.col-3 {
              width: 480px;
            }
          }

          .header {
            font-size: 16px;
            color: #333;
            margin-bottom: 20px;
            margin-top: 15px;
            vertical-align: middle;
            letter-spacing: 1px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .tips {
              color: #999;
            }
          }

        }
      }

    }
    .module-footer {
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      height: 80px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      .box-price {
        min-width: 140px;
        height: 40px;
        background: linear-gradient(30deg, #f7debe, #f4c384);
        border-radius: 5px;
        margin: 0;
        padding: 5px 10px 5px 12px;
        box-sizing: border-box;
        color: #9a5b12;
        transition: opacity 0.1s ease-in-out;
        cursor: pointer;
        font-size: 13px;
        letter-spacing: 1px;
        display: flex;
        align-items: center;
        overflow: hidden;
        white-space: nowrap;

        &:hover {
          opacity: 0.8;
        }

        .icon {
          font-size: 18px;
          margin-right: 5px;
        }
        .num {
          font-size: 16px;
          font-weight: bold;
          margin: 0 5px;
        }
        .vip {
          font-size: 14px;
          font-weight: bold;
          margin: 0 5px;
        }
      }
    }
  }
  .size-big {
    .sub-sidebar {
      width: 755px;
    }
    .main-wrapper {
      left: 755px;
    }
    .music-setting {
      width: 100%;
      .module-footer {
        .box-wallet {
          width: 200px;
        }
      }
    }
    .wrapper {
      left: 755px;
    }
  }
  .size-small {
    .sub-sidebar {
      width: 500px;
    }
    .main-wrapper {
      left: 500px;
    }
    .music-setting {
      width: 100%;
      .box-music-setting {
        width: 100%;
      }
      .module-footer {
        .box-wallet {
          width: 200px;
        }
      }
    }
    .wrapper {
      left: 265px;
    }
  }

  .platform-list {
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 10px;
    .item {
      position: relative;
      height: 48px;
      line-height: 36px;
      padding: 0 25px;
      font-size: 16px;
      font-weight: bold;
      white-space: nowrap;
      cursor: pointer;
      transition: top 0.1s linear, background 0.2s linear, color 0.2s linear;
      background: none;
      top: 2px;
      color: #666;
      &.active {
        background: linear-gradient(#e3f5f0 0%, #f7f7f8 75%);
        height: 55px;
        line-height: 42px;
        top: 0;
        box-shadow: 0 -5px 20px 0 rgba(0,0,0,0.1);
        border-radius: 10px 10px 0 0;
        color: #10a37f;
      }
      &:before, &:after {
        content: '';
        display: none;
        width: 10px;
        height: 100%;
        background: linear-gradient(#e3f5f0 0%, #f7f7f8 75%);
        position: absolute;
        left: -5px;
        top: 0;
        z-index: 2;
        border-radius: 10px 10px 0 0;
        transform: skew(-10deg);
      }
      &:after {
        left: auto;
        right: -5px;
        top: 0;
        //border-top-left-radius: 0;
        transform: skew(10deg);
      }
      &.active:before, &.active:after{
        display: block;
      }
      &:first-child {
        border-top-left-radius: 10px;
        &.active {
          box-shadow: 6px -5px 16px 0 rgba(0,0,0,0.1);
        }
        &.active:before {
          display: none;
        }
      }
      &:last-child {
        border-top-right-radius: 10px;
      }
    }

  }

  .type-list {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    z-index: 999;
    .tab-type {
      height: 36px;
      border-radius: 5px;
      box-sizing: border-box;
      z-index: 99;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 0 10px rgb(0, 0, 0, 0.1);
      transition: background 0.1s ease-in-out;
      overflow: hidden;

      .tab-item {
        height: 36px;
        line-height: 36px;
        text-align: center;
        font-size: 15px;
        cursor: pointer;
        transition: all 0.1s ease-in-out;
        padding: 0 15px;
        background: #fff;
        color: #333;
        &.active {
          background: #10a37f;
          color: #fff;
        }
        &:first-child {
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
        }
        &:last-child {
          border-top-right-radius: 5px;
          border-bottom-right-radius: 5px;
        }
      }
    }
  }
  /** 作品广场列表 **/
  .music-list {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    margin-top: 70px;
    margin-bottom: 30px;
    //transition: all 0.1s linear;
    display: grid;
    gap: 12px;
    .music-item {
      display: flex;
      flex-direction: column;
      transition: width 0.2s linear;
      gap: 12px;
      overflow: hidden;
      box-sizing: border-box;
      //background: #f7f7f8;
      //border-radius: 5px;
      .music {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: calc(56.306%);
        background: #1f1f1f;
        text-align: center;
        border-radius: 5px;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        .poster {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background-size: cover;
          background-position: center center;
          background-repeat: no-repeat;
          z-index: 1;
          opacity: 1;
          transition: all 0.2s linear;
        }
        &.active {
          .poster {
            opacity: 0;
          }
          music {
            opacity: 1;
          }
          .prompt {
            bottom: 0;
          }
          .btn-play {
            opacity: 0;
          }
        }
      }

    }
  }


}
@media (min-width: 800px) {
  .module-music .music-list {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
@media (min-width: 1100px) {
  .module-music .music-list {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (min-width: 1300px) {
  .module-music .music-list {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

