.sidebar-container {
  //transition: width 0.28s;
  width: 100px;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  box-sizing: border-box;

  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }

  .el-scrollbar__bar.is-vertical {
    right: 0;
    width: 3px;
  }

  .el-scrollbar {
    height: 100%;
  }

  &.has-logo {
    .el-scrollbar {
      height: calc(100% - 50px);
    }
  }

  a {
    display: inline-block;
    width: 100%;
    overflow: hidden;
  }

  .svg-icon {
    margin-right: 14px;
  }

  .sub-el-icon {
    margin-right: 12px;
    margin-left: -2px;
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
  }
}

.sidebar-container {
  .el-input__inner {
    transition: background 0.1s ease-in-out, border-color 0.1s ease-in-out, color 0.1s ease-in-out;
  }
  .avatar {
    width: 48px;
    height: 48px;
    border-radius: 5px;
    margin: 10px auto 30px auto;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .module-list {
    position: fixed;
    left: 0;
    top: 0;
    width: 100px;
    bottom: 0;
    padding: 20px 10px;
    transition: all 0.1s ease-in-out;
    .module-item {
      width: 80px;
      height: 80px;
      padding: 15px;
      border-radius: 10px;
      font-size: 13px;
      color: #72787e;
      cursor: pointer;
      text-align: center;
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      transition: all 0.1s ease-in-out;
      .icon {
        font-size: 30px;
        margin: 0 auto;
      }
      &:hover, &.active {
        color: #10a37f;
      }
    }
  }
  .menu-bottom {
    position: absolute;
    bottom: 10px;
    .module-item {
      height: 66px;
      font-size: 14px;
      margin-top: 10px;
      padding: 10px 15px;
    }
  }
}
@media (max-height: 700px) {
  .sidebar-container .module-list .module-item {
    margin-top: 0;
    padding: 10px 15px;
    height: 70px;
  }
}
@media (max-height: 600px) {
  .sidebar-container .menu-bottom .module-item {
    display: none;
  }
}
