.sub-sidebar {
  width: 250px;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background: #f7f7f8;
  border-radius: 10px;
  overflow: hidden;
  transition: background-color 0.1s ease-in-out;

  .el-scrollbar {
    height: 100%;
  }

  .module-header {
    width: 100%;
    padding: 15px 15px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }

  .module-body {
    position: absolute;
    left: 0;
    right: 0;
    top: 62px;
    bottom: 73px;
    padding: 0;
  }

  .module-footer {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 73px;

    .box-vip {
      width: 230px;
      height: 50px;
      background: linear-gradient(30deg, #f7debe, #f4c384);
      border-radius: 10px;
      margin: 10px;
      padding: 8px 10px 5px 20px;
      box-sizing: border-box;
      color: #9a5b12;
      transition: opacity 0.1s ease-in-out;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }

      .title {
        line-height: 18px;
        font-size: 14px;
        font-weight: bold;
        letter-spacing: 1px;
      }

      .desc {
        width: 100%;
        line-height: 18px;
        font-size: 13px;
      }

      .icon {
        position: absolute;
        right: 30px;
        top: 20px;
        width: 30px;
        height: 30px;
        z-index: 1;
      }
    }
  }
}

.module-chat {
  .sub-sidebar {
    .module-header {
      .module-title {
        font-size: 14px;
        line-height: 32px;
        display: flex;
        justify-content: space-between;
        transition: color 0.1s ease-in-out;
      }

      .btn-group-add {
        width: 100%;
        color: #10a37f;
        font-size: 14px;
        display: flex;
        align-items: center;
        cursor: pointer;
        height: 40px;
        border-radius: 4px;
        padding: 0 15px;
        box-sizing: border-box;
        border: 1px solid #ddd;
        background: #fff;
        transition: background 0.1s ease-in-out, border-color 0.1s ease-in-out;

        .icon {
          font-size: 16px;
          margin-right: 5px;
        }

        &:hover {
          background-color: #e3f5f0;
          border-color: #e3f5f0;
          color: #10a37f !important;
        }
      }
    }

    .group-chat {
      padding: 20px 15px 0 15px;

      .group-item {
        width: 100%;
        height: 44px;
        border-radius: 8px;
        margin-bottom: 5px;
        position: relative;
        transition: background 0.1s ease-in-out;

        .group-title {
          color: #72787e;
          font-size: 15px;
          white-space: nowrap;
          overflow: hidden;
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 10px 15px;
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          transition: color 0.1s ease-in-out;

          .icon {
            font-size: 18px;
            margin-right: 8px;
          }

          span {
            white-space: nowrap;
          }
        }

        .el-dropdown {
          display: none;
          position: absolute;
          right: 0;
          top: 6px;
        }

        .btn-dropdown {
          transform: rotate(90deg);
          color: #666;
          font-size: 14px;
          display: block;
          padding: 10px;
        }

        &:hover {
          background: #eff0f0;

          .el-dropdown {
            display: block;
          }
        }

        &.active {
          background: #eff0f0;

          .btn-dropdown {
            background: #eff0f0;
          }
        }
      }
    }
  }
}

.module-write {
  .sub-sidebar {
    .prompt-list-container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .category-list {
      padding: 10px 0;
    }

    .category-group {
      margin-bottom: 5px;
    }

    .category-item {
      padding: 8px 15px;
      cursor: pointer;
      font-size: 13px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: all 0.2s ease-in-out;

      &.primary-category {
        font-weight: 500;
        color: #333;

        .category-content {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;

          .category-title {
            border-left: 3px solid #10a37f;
            padding-left: 8px;
          }

          .category-count {
            font-size: 11px;
            color: #999;
          }
        }

        .expand-icon {
          margin-left: 8px;

          i {
            font-size: 12px;
            transition: transform 0.2s ease-in-out;
            color: #999;

            &.expanded {
              transform: rotate(90deg);
            }
          }
        }
      }

      &.sub-category {
        padding-left: 25px;
        color: #666;
        font-size: 12px;

        .category-count {
          font-size: 11px;
          color: #999;
        }
      }

      &:hover {
        background: #e8f5e8;
        color: #10a37f;

        .expand-icon i {
          color: #10a37f;
        }
      }

      &.active {
        background: #e8f5e8;
        color: #10a37f;

        .category-title {
          border-color: #10a37f;
        }

        .expand-icon i {
          color: #10a37f;
        }
      }
    }

    .sub-categories {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-in-out;

      &.expanded {
        max-height: 500px; // 足够大的值来容纳所有子分类
      }
    }
  }

  // 外部内容展示区域
  .content-area-external {
    position: fixed;
    left: 355px;
    width: 45vw;
    max-width: calc(100vw - 380px);
    width: min(45vw, calc(100vw - 380px));
    top: 15px;
    bottom: 15px;
    background: #fff;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: background-color 0.1s ease-in-out;
    z-index: 9999;

    @media (max-width: 1400px) {
      width: calc(100vw - 380px);
    }

    .content-header {
      padding: 15px 20px 10px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        display: flex;
        flex-direction: column;
        gap: 5px;

        .selected-category-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin: 0;
        }

        .prompt-count {
          font-size: 12px;
          color: #999;
        }
      }

      .header-right {
        .close-btn {
          background: none;
          border: none;
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;
          color: #666;
          transition: all 0.2s ease-in-out;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: #f0f0f0;
            color: #333;
          }

          i {
            font-size: 16px;
          }
        }
      }
    }

    .prompt-grid {
      padding: 15px 20px;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 250px));
      gap: 15px;
      justify-content: flex-start;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #999;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      .empty-text {
        font-size: 14px;
        color: #666;
      }
    }

    .prompt-item {
      background: #f9f9f9;
      border: 2px solid #f9f9f9;
      border-radius: 8px;
      padding: 15px;
      width: 250px;
      cursor: pointer;
      transition: all 0.2s ease-in-out;
      display: flex;
      flex-direction: column;
      height: 215px;

      &:hover {
        border-color: #e0e0e0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &.active {
        border-color: #10a37f;
        box-shadow: 0 2px 8px rgba(16, 163, 127, 0.2);
      }

      &.hidden {
        display: none;
      }

      .prompt-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
        line-height: 1.4;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .prompt-desc {
        font-size: 12px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 10px;
        flex: 1;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
      }

      .prompt-ops {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: transparent;
        padding: 0;
        margin-top: auto;

        .op-item {
          display: flex;
          align-items: center;
          font-size: 11px;
          color: #999;
          cursor: pointer;

          .icon {
            font-size: 12px;
            margin-right: 3px;
          }

          &:hover {
            color: #10a37f;
          }
        }
      }
    }
  }
}

.module-cosplay {
  .sub-sidebar {
    .fixed-topic {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
      background: #f7f7f8;
      width: 100%;
      transition: background 0.1s ease-in-out;
    }

    .group-cosplay {
      padding: 0 15px;

      .topic-title {
        height: 48px;
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;

        span {
          border-left: 4px solid #10a37f;
          padding-left: 8px;
          line-height: 16px;
        }

        .collapse {
          font-size: 18px;
          margin: 0;
          transition: all 0.2s ease-in-out;
        }
      }

      .is_collapse {
        .collapse {
          transform: rotate(-90deg);
        }
      }

      .role-list {
        width: 100%;
        transition: all 0.2s ease-in-out;
        overflow: hidden;
      }

      .role-item {
        padding: 10px 10px;
        border-radius: 8px;
        margin-bottom: 12px;
        display: flex;
        border: 2px solid #eff0f0;
        cursor: pointer;
        transition: background 0.1s ease-in-out, border-color 0.1s ease-in-out;
        height: 68px;
        box-sizing: border-box;

        &.hidden {
          display: none;
        }

        .thumb {
          width: 44px;
          height: 44px;
          margin-right: 10px;
          border-radius: 5px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .role-title {
          font-size: 14px;
          font-weight: bold;
          height: 22px;
          line-height: 22px;
          overflow: hidden;
          //display: -webkit-box;
          //-webkit-box-orient: vertical;
          //-webkit-line-clamp: 2;
          transition: color 0.1s ease-in-out;
        }

        .role-ops {
          display: flex;
          align-items: center;
          margin-top: 7px;
          height: 16px;

          .op-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            margin-right: 10px;
            transition: color 0.1s ease-in-out;

            .icon {
              width: 14px;
              height: 14px;
              margin-right: 5px;
            }
          }
        }
      }
    }
  }
}

.module-apps {
  .apps {
    .header {
      width: 100%;
      font-size: 16px;
      color: #333;
      vertical-align: middle;
      letter-spacing: 1px;
      position: absolute;
      left: 0;
      top: 0;
      padding: 30px 20px 10px 20px;
    }
    .app-list {
      padding: 15px;
      box-sizing: border-box;

      .app-item {
        padding: 15px 10px;
        border-radius: 8px;
        margin-bottom: 12px;
        display: flex;
        border: 2px solid #eff0f0;
        cursor: pointer;
        transition: background 0.1s ease-in-out, border-color 0.1s ease-in-out;
        height: 78px;
        box-sizing: border-box;

        .thumb {
          width: 44px;
          height: 44px;
          margin-right: 10px;
          border-radius: 5px;
          overflow: hidden;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .title {
          font-size: 14px;
          font-weight: bold;
          height: 22px;
          line-height: 22px;
          overflow: hidden;
          //display: -webkit-box;
          //-webkit-box-orient: vertical;
          //-webkit-line-clamp: 2;
          transition: color 0.1s ease-in-out;
        }
        .desc {
          font-size: 12px;
          color: #888;
          margin-top: 5px;
          line-height: 18px;
          height: 54px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          transition: color 0.1s ease-in-out;
        }

      }

    }
  }
}
