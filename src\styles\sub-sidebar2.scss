.sub-sidebar {
  width: 250px;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background: #f7f7f8;
  border-radius: 10px;
  overflow: hidden;
  transition: background-color 0.1s ease-in-out;

  .el-scrollbar {
    height: 100%;
  }

  .module-header {
    width: 100%;
    padding: 15px 15px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }

  .module-body {
    position: absolute;
    left: 0;
    right: 0;
    top: 62px;
    bottom: 80px;
    padding: 0;
  }

  .module-footer {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 80px;

    .box-wallet {
      width: 230px;
      height: 60px;
      margin: 10px;
      border: none;
      position: relative;
      background: linear-gradient(30deg, #f7debe, #f4c384);
      border-radius: 10px;
      color: #9a5b12;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;

      .vip {
        width: 154px;
        height: 100%;
        display: flex;
        align-items: flex-start;
        padding: 13px 0 0 15px;
        border-radius: 10px 0 0 10px;
        cursor: pointer;
        &:hover, &:active {
          background: rgba(255, 255, 255, 0.3);
        }
        .icon {
          width: 18px;
          height: 18px;
          margin-right: 5px;
        }
        .line {
          margin: 0 10px;
          vertical-align: middle;
          display: inline-block;
          width: 1px;
          height: 12px;
          background: rgba(154, 91, 18, 0.4);
        }
        p {
          margin: 0;
        }
        .title {
          line-height: 18px;
          font-size: 14px;
          font-weight: bold;
          letter-spacing: 1px;
        }
        .desc {
          width: 100%;
          line-height: 18px;
          font-size: 13px;
        }
        .btn-vip {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 72px;
          height: 24px;
          background: #ffe8b5;
          border-radius: 12px;
          font-size: 12px;
          padding: 0;
          border: none;
          color: #9a5b12;
          cursor: pointer;
          &:hover {
            opacity: 0.8;
          }
        }
      }
      .point {
        text-align: center;
        padding: 10px 5px;
        width: 76px;
        box-sizing: border-box;
        cursor: pointer;
        border-radius: 0 10px 10px 0;
        line-height: 18px;
        &:hover, &:active {
          background: rgba(255, 255, 255, 0.15);
        }
        .num {
          font-size: 14px;
          color: #9a5b12;
          font-weight: bold;
          white-space: nowrap;
        }
        .label {
          font-size: 13px;
          white-space: nowrap;
        }
      }
    }

    /*.box-vip {
      width: 230px;
      height: 50px;
      background: linear-gradient(30deg, #f7debe, #f4c384);
      border-radius: 10px;
      margin: 10px;
      padding: 8px 10px 5px 20px;
      box-sizing: border-box;
      color: #9a5b12;
      transition: opacity 0.1s ease-in-out;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }

      .title {
        line-height: 18px;
        font-size: 14px;
        font-weight: bold;
        letter-spacing: 1px;
      }

      .desc {
        width: 100%;
        line-height: 18px;
        font-size: 13px;
      }

      .icon {
        position: absolute;
        right: 30px;
        top: 20px;
        width: 30px;
        height: 30px;
        z-index: 1;
      }
    }

    .box-wallet {
      width: 430px;
      height: 40px;
      background: linear-gradient(30deg, #f7debe, #f4c384);
      border-radius: 5px;
      margin: 10px;
      padding: 8px 10px 5px 15px;
      box-sizing: border-box;
      color: #9a5b12;
      transition: opacity 0.1s ease-in-out;
      cursor: pointer;
      font-size: 14px;
      letter-spacing: 1px;
      display: flex;
      align-items: center;

      &:hover {
        opacity: 0.8;
      }

      .icon {
        font-size: 18px;
        margin-right: 5px;
      }

      span {
        font-size: 16px;
        font-weight: bold;
        margin: 0 5px;
      }
    }*/
  }
}

.module-chat {
  .sub-sidebar {
    .module-header {
      .module-title {
        font-size: 14px;
        line-height: 32px;
        display: flex;
        justify-content: space-between;
        transition: color 0.1s ease-in-out;
      }

      .btn-group-add {
        width: 100%;
        color: #10a37f;
        font-size: 14px;
        display: flex;
        align-items: center;
        cursor: pointer;
        height: 40px;
        border-radius: 4px;
        padding: 0 15px;
        box-sizing: border-box;
        border: 1px solid #ddd;
        background: #fff;
        transition: background 0.1s ease-in-out, border-color 0.1s ease-in-out;

        .icon {
          font-size: 16px;
          margin-right: 5px;
        }

        &:hover {
          background-color: #e3f5f0;
          border-color: #e3f5f0;
          color: #10a37f !important;
        }
      }
    }

    .group-chat {
      padding: 20px 15px 0 15px;

      .group-item {
        width: 100%;
        height: 44px;
        border-radius: 8px;
        margin-bottom: 5px;
        position: relative;
        transition: background 0.1s ease-in-out;

        .group-title {
          color: #72787e;
          font-size: 15px;
          white-space: nowrap;
          overflow: hidden;
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 10px 15px;
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          transition: color 0.1s ease-in-out;

          .icon {
            font-size: 18px;
            margin-right: 8px;
          }

          span {
            white-space: nowrap;
          }
        }

        .el-dropdown {
          display: none;
          position: absolute;
          right: 0;
          top: 6px;
        }

        .btn-dropdown {
          transform: rotate(90deg);
          color: #666;
          font-size: 14px;
          display: block;
          padding: 10px;
        }

        &:hover {
          background: #eff0f0;

          .el-dropdown {
            display: block;
          }
        }

        &.active {
          background: #eff0f0;

          .btn-dropdown {
            background: #eff0f0;
          }
        }
      }
    }
  }
}

.module-write {
  .sub-sidebar {
    .fixed-topic {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
      background: #f7f7f8;
      width: 100%;
      transition: background 0.1s ease-in-out;
    }

    .group-write {
      padding: 0 15px;

      .topic-title {
        height: 48px;
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;

        span {
          border-left: 4px solid #10a37f;
          padding-left: 8px;
          line-height: 16px;
        }

        .collapse {
          font-size: 18px;
          margin: 0;
          transition: all 0.2s ease-in-out;
        }
      }

      .is_collapse {
        .collapse {
          transform: rotate(-90deg);
        }
      }

      .prompt-list {
        width: 100%;
        transition: all 0.5s ease-in-out;
        overflow: hidden;
      }

      .prompt-item {
        padding: 10px 15px;
        border-radius: 8px;
        margin-bottom: 12px;
        height: 108px;
        overflow: hidden;
        position: relative;
        border: 2px solid #eff0f0;
        box-sizing: border-box;
        cursor: pointer;
        transition: background 0.1s ease-in-out, border-color 0.1s ease-in-out;

        &.hidden {
          display: none;
        }

        .prompt-title {
          font-size: 14px;
          font-weight: bold;
          line-height: 22px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          transition: color 0.1s ease-in-out;
        }

        .prompt-desc {
          font-size: 12px;
          color: #888;
          margin-top: 5px;
          line-height: 18px;
          height: 54px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          transition: color 0.1s ease-in-out;
        }

        .prompt-ops {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 32px;
          position: absolute;
          width: 100%;
          left: 0;
          bottom: 0;
          padding: 5px 15px;
          box-sizing: border-box;
          transition: background 0.1s ease-in-out;

          .op-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            transition: color 0.1s ease-in-out;

            .icon {
              width: 14px;
              height: 14px;
              margin-right: 5px;
            }
          }
        }
      }
    }
  }
}

.module-cosplay {
  .sub-sidebar {
    .fixed-topic {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
      background: #f7f7f8;
      width: 100%;
      transition: background 0.1s ease-in-out;
    }

    .group-cosplay {
      padding: 0 15px;

      .topic-title {
        height: 48px;
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;

        span {
          border-left: 4px solid #10a37f;
          padding-left: 8px;
          line-height: 16px;
        }

        .collapse {
          font-size: 18px;
          margin: 0;
          transition: all 0.2s ease-in-out;
        }
      }

      .is_collapse {
        .collapse {
          transform: rotate(-90deg);
        }
      }

      .role-list {
        width: 100%;
        transition: all 0.2s ease-in-out;
        overflow: hidden;
      }

      .role-item {
        padding: 10px 10px;
        border-radius: 8px;
        margin-bottom: 12px;
        display: flex;
        border: 2px solid #eff0f0;
        cursor: pointer;
        transition: background 0.1s ease-in-out, border-color 0.1s ease-in-out;
        height: 68px;
        box-sizing: border-box;

        &.hidden {
          display: none;
        }

        .thumb {
          width: 44px;
          height: 44px;
          margin-right: 10px;
          border-radius: 5px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .role-title {
          font-size: 14px;
          font-weight: bold;
          height: 22px;
          line-height: 22px;
          overflow: hidden;
          //display: -webkit-box;
          //-webkit-box-orient: vertical;
          //-webkit-line-clamp: 2;
          transition: color 0.1s ease-in-out;
        }

        .role-ops {
          display: flex;
          align-items: center;
          margin-top: 7px;
          height: 16px;

          .op-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            margin-right: 10px;
            transition: color 0.1s ease-in-out;

            .icon {
              width: 14px;
              height: 14px;
              margin-right: 5px;
            }
          }
        }
      }
    }
  }
}

.module-apps {
  .apps {
    .header {
      width: 100%;
      font-size: 16px;
      color: #333;
      vertical-align: middle;
      letter-spacing: 1px;
      position: absolute;
      left: 0;
      top: 0;
      padding: 30px 20px 10px 20px;
    }
    .app-list {
      padding: 15px;
      box-sizing: border-box;

      .app-item {
        padding: 15px 10px;
        border-radius: 8px;
        margin-bottom: 12px;
        display: flex;
        border: 2px solid #eff0f0;
        cursor: pointer;
        transition: background 0.1s ease-in-out, border-color 0.1s ease-in-out;
        height: 78px;
        box-sizing: border-box;

        .thumb {
          width: 44px;
          height: 44px;
          margin-right: 10px;
          border-radius: 5px;
          overflow: hidden;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .title {
          font-size: 14px;
          font-weight: bold;
          height: 22px;
          line-height: 22px;
          overflow: hidden;
          //display: -webkit-box;
          //-webkit-box-orient: vertical;
          //-webkit-line-clamp: 2;
          transition: color 0.1s ease-in-out;
        }
        .desc {
          font-size: 12px;
          color: #888;
          margin-top: 5px;
          line-height: 18px;
          height: 54px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          transition: color 0.1s ease-in-out;
        }

      }

    }
  }
}
