.theme-dark {
  background: #262626;

  .sidebar-container {
    .el-input__inner {
      background: #262626;
      border-color: #262626;
      color: #acafac;
    }

    .module-list {
      .module-item {
        color: #acafac;

        &:hover, &.active {
          color: #10a37f;
        }
      }
    }
  }

  .sub-sidebar {
    background: #333333;

    .module-footer {
      .box-vip {
        opacity: 0.6;

        &:hover {
          opacity: 0.8;
        }
      }

      .box-wallet {
        opacity: 0.6;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  .module-chat {
    .sub-sidebar {
      .module-header {
        .btn-group-add {
          background: #333333;
          border-color: #333333;
        }
      }

      .group-chat {
        .group-item {
          &.active, &:hover {
            background: #262626;
          }

          .group-title {
            color: #acafac;
          }

          .btn-dropdown {
            background: #262626;
          }
        }
      }
    }
  }

  .module-write {
    .sub-sidebar {
      .category-item {
        &.primary-category {
          color: #acafac;

          .expand-icon i {
            color: #888;
          }

          &:hover .expand-icon i {
            color: #10a37f;
          }

          &.active .expand-icon i {
            color: #10a37f;
          }
        }

        &.sub-category {
          color: #888;
        }

        &:hover {
          background: #404040;
          color: #10a37f;
        }

        &.active {
          background: #404040;
          color: #10a37f;
        }
      }
    }

    .content-area-external {
      background: #262626;

      .content-header {
        border-bottom-color: #444;

        .selected-category-title {
          color: #acafac;
        }

        .prompt-count {
          color: #888;
        }

        .close-btn {
          color: #888;

          &:hover {
            background: #404040;
            color: #acafac;
          }
        }
      }

      .prompt-item {
        background: #333;
        border-color: #333;

        &:hover {
          border-color: #555;
        }

        &.active {
          border-color: #10a37f;
        }

        .prompt-title {
          color: #acafac;
        }

        .prompt-desc {
          color: #a0a0a0;
        }

        .prompt-ops {
          .op-item {
            color: #888;

            &:hover {
              color: #10a37f;
            }
          }
        }
      }

      .empty-state {
        .empty-text {
          color: #888;
        }
      }
    }
  }

  .module-cosplay {
    .sub-sidebar {
      .fixed-topic {
        background: #333;
      }

      .group-cosplay {
        .topic-title {
          color: #acacac;

          span {
            border-color: #10a37f;
          }
        }

        .role-item {
          background: #262626;
          border-color: #262626;

          &.active {
            border-color: #10a37f;
          }

          .role-title {
            color: #acafac;
          }

          .role-ops {
            .op-item {
              color: #a0a0a0;
            }
          }
        }
      }
    }
  }

  .main-container {
    .main-wrapper {
      .tab-ai {
        background: #333333;

        .tab-item {
          border: 1px solid #262626;
          color: #acafac;

          &:hover, &.active {
            color: #10a37f;
            border: 1px solid #10a37f;
          }
        }
      }

      .box-msg-list {
        .list {
          .row-ai {
            background: #262626;
          }

          .row-user {
            background: #333333;
          }

          .text {
            color: #acafac;
          }

          .image {
            border-color: #262626;
          }

          .mj-ctrl {
            .title {
              color: #acafac;
            }

            span {
              background: #262626;
              color: #acacac;
            }
          }
        }
      }

      .gold {
        .tab-ai {
          .active {
            border: 1px solid #f5ad0c !important;
            color: #f5ad0c !important;
          }
        }

        .box-input {
          .input {
            textarea {
              border: 1px solid #ffa41a !important;
            }
          }
        }
      }

      .style-chat {
        .list {
          .row-user {
            background: none;

            .message {
              .text {
                background: rgba(16, 163, 127, 0.5);
                color: #cacfca;
              }
            }
          }

          .row-ai {
            background: none;

            .message {
              .text {
                background: #333333;
                color: #acafac;
              }
            }
          }
        }
      }

      .style-write {
        .list {
          .row-user {
            background: #333333;
            border-bottom: 1px solid #2c2c2c;
          }

          .row-ai {
            background: #333333;
          }
        }

      }

      .box-input {
        //background: #262626;

        .input {
          box-shadow: 0 0 15px rgba(255, 255, 255, 0.05);

          .el-textarea__inner {
            background: #262626;
            border-color: #777777;
            color: #acafac;

            &::-webkit-input-placeholder {
              color: #777777;
            }

            &:focus {
              border-color: #10a37f;
            }
          }
        }

        .btn-send {
          background: #262626 !important;
          color: #acafac;

          &:hover {
            color: #10a37f;
          }
        }

        .copyright {
          color: #777777;
        }
      }

      .markdown-body {
        img[alt=cursor] {
          filter: invert(100%);
        }

        .message-reply {
          .whitespace-pre-wrap {
            white-space: pre-wrap;
            color: var(--n-text-color);
          }
        }

        .highlight pre,
        pre {
          background-color: #202020;
        }

        /*highlight pre, .markdown-body pre {
           background: #202020;
         }*/
        table {
          tr {
            background-color: #262626;

            &:nth-child(2n) {
              background-color: #333333;
            }
          }

          th, td {
            border-color: #777777;
          }
        }

        .code-linenum-line {
          color: #666;
        }
      }

      .welcome {
        .title {
          color: #acafac;
        }

        .h-item {
          color: #acafac !important;
        }

        .tips {
          li {
            background: #262626;
            color: #acafac;

            &:hover {
              background: #262626;
              color: #10a37f;
            }
          }
        }

        .tips-list {
          li {
            background: #333;
            color: #acafac;

            &:active {
              background: #262626;
            }
          }
        }

      }

      .style-word {
        .list {

          .row-user {
            .message {
              background: #333;
              .text {
                color: #acafac;
              }
            }
          }
          .row-ai {
            .message {
              background: #333;
              .text {
                color: #acafac;
              }
            }
          }
        }
      }

      .pages {
        span {
          border: 1px solid #777;
          background: #262626;
          color: #acafac;
          &.active {
            border-color: #10a37f;
          }
        }
      }

    }
  }

  .module-apps {
    .sub-sidebar {
      .apps {
        .header {
          color: #acafac;
        }
      }

      .app-list {
        .app-item {
          background: #262626;
          border-color: #262626;
          &.active {
            border-color: #10a37f;
          }
          .title {
            color: #acafac;
          }
          .thumb {
            color: #acafac;
          }
        }
      }
    }

    .main-wrapper {
      background: #262626;

      .main-pk {
        .box-msg-list {
          .box-a {
            background: #333;
            border: none;
          }
          .box-b {
            background: #333;
            border: none;
          }
          .list {
            .row {
              .row-ai .message .text {
                background: #262626 !important;
              }
            }
          }
        }

        .box-input {
          .select {
            .el-select {
              box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
              .el-input__inner {
                background: #262626;
                color: #acafac;
                border-color: #777;
              }
            }
            p {
              margin: 0;
              font-size: 15px;
              color: #72787e;
              margin-bottom: 10px;
            }
          }
        }
      }

      .main-batch {
        background: #333;
        .batch-list {
          .batch-item {
            background: #262626;
            border: 1px solid #444;
            &:hover {
              background: #222;
            }
            .header {
              .count {
                color: #acafac;
              }
            }
            .body {
              color: #888;
            }
            .footer {
              .time {
                color: #888;
              }
              .ops {
                color: #888;
              }
            }
          }
          .btn-add {
            color: #666;
          }
        }

        .box-task {
          .task-main {
            .task-list {
              .task-item {
                border-top: 1px solid #262626;
                &:hover, &.active {
                  background: #262626;
                }
                .index {
                  color: #acafac;
                }
                .title {
                  color: #acafac;
                }
              }
            }

            .task-chat {
              background: #262626;
              border: 1px solid #262626;
            }
          }

          .task-empty {
            border-top: 1px solid #262626;
            .box-empty {
              width: 200px;
              .icon {
                color: #acafac;
              }
              .tips {
                color: #777;
              }
            }
          }
        }

      }

      .main-novel {
        background: #333;
        .novel-list {
          .novel-item {
            background: #262626;
            border: 1px solid #444;
            &:hover {
              background: #222;
            }
            .title {
              color: #acafac;
            }
            .count {
              color: #acafac;
            }
            .time {
              color: #888;
            }
          }
          .btn-add {
            color: #666;
          }
        }

        .box-task {
          .task-main {
            .task-list {
              .task-item {
                border-top: 1px solid #262626;
                &:hover, &.active {
                  background: #262626;
                }
                .index {
                  color: #acafac;
                }
                .title {
                  color: #acafac;
                }
              }
            }

            .task-chat {
              background: #262626;
              border: 1px solid #262626;
              .overview {
                background: #2b2b2b;
                color: #909090;
              }
            }
          }

          .task-empty {
            border-top: 1px solid #262626;
            .box-empty {
              width: 200px;
              .icon {
                color: #acafac;
              }
              .tips {
                color: #777;
              }
            }
          }
        }

      }
    }
  }

  .float {
    .btn {
      background: #333 !important;
      border-color: #777 !important;
      color: #acafac !important;

      &:hover {
        border-color: #10a37f !important;
        color: #10a37f !important;
      }
    }
  }

  .module-draw {
    .writing {
      background: #2c2c2c;
    }
    .welcome .nodata img {
      opacity: 0.6;
    }

    .fail {
      .errmsg {
        color: #bbb;
      }
      .btn-retry {
        background: #444;
        color: #acafac;
      }
    }

    .draw-setting {
      background: #333;

      .box-draw-setting {
        .setting-row {
          .setting-item {
            background: #262626;
            .header {
              color: #acafac !important;
            }

            .options {
              textarea {
                background: #262626;
                border-color: #777777;
                color: #acafac;
              }

              .el-upload {
                background: #262626;
              }
            }

            .size-item {
              border-color: #777;
              background-color: #333;
              .size-block {
                background-color: #777;
              }
              &.active {
                border-color: #10a37f;
              }

              span {
                color: #fff;
              }
            }
          }

          .box-prompt .words-cate .item {
            background: #333;
            color: #acacac;
            &:hover {
              background: #555;
            }
          }
        }
      }
      .el-input__inner, .el-textarea__inner, .el-radio-button__inner {
        background: #151511;
        border-color: #777777;
        color: #acafac;
      }
      .is-active .el-radio-button__inner {
        color: #fff;
      }
    }
  }

  .module-video {
    .writing {
      background: #2c2c2c;
    }

    .welcome .nodata img {
      opacity: 0.6;
    }

    .fail {
      .errmsg {
        color: #bbb;
      }

      .btn-retry {
        background: #444;
        color: #acafac;
      }
    }

    .video-setting {
      background: #333;

      .box-video-setting {
        .uploader {
          border-color: #777777;
        }
        .setting-row {
          .setting-item {
            background: #262626;
            .header {
              color: #acafac !important;
            }

            .options {
              textarea {
                background: #262626;
                border-color: #777777;
                color: #acafac;
              }

              .el-upload {
                background: #262626;
              }
            }

            .size-item {
              border-color: #777;
              background-color: #333;
              .size-block {
                background-color: #777;
              }
              &.active {
                border-color: #10a37f;
              }

              span {
                color: #fff;
              }
            }
          }

          .box-prompt .words-cate .item {
            background: #333;
            color: #acacac;
            &:hover {
              background: #555;
            }
          }
        }
        .option-radio-group {
          .item {
            .label {
              color: #909399 !important;
            }
          }
        }
      }
      .el-input__inner, .el-textarea__inner, .el-radio-button__inner {
        background: #151511;
        border-color: #777777;
        color: #acafac;
      }
      .is-active .el-radio-button__inner {
        color: #fff;
      }
    }
    .platform-list {
      width: 100%;
      display: flex;
      align-items: center;
      margin-top: 10px;
      .item {
        color: #acafac;
        &.active {
          background: #333;
          color: #10a37f;
        }
        &:before, &:after {
          background: #333;
        }
      }
    }
  }

  .module-music {
    .writing {
      background: #2c2c2c;
    }

    .welcome .nodata img {
      opacity: 0.6;
    }

    .fail {
      .errmsg {
        color: #bbb;
      }

      .btn-retry {
        background: #444;
        color: #acafac;
      }
    }

    .music-setting {
      background: #333;

      .box-music-setting {
        .setting-row {
          .setting-item {
            background: #262626;
            .header {
              color: #acafac !important;
            }

            .options {
              textarea {
                background: #262626;
                border-color: #777777;
                color: #acafac;
              }

              .el-upload {
                background: #262626;
              }
            }
          }

          .box-prompt .words-cate .item {
            background: #333;
            color: #acacac;
            &:hover {
              background: #555;
            }
          }
        }
        .option-radio-group {
          .item {
            .label {
              color: #909399 !important;
            }
          }
        }
      }
      .el-input__inner, .el-textarea__inner, .el-radio-button__inner {
        background: #151511;
        border-color: #777777;
        color: #acafac;
      }
      .el-textarea .el-input__count {
        color: #acafac;
        background: none;
      }
      .is-active .el-radio-button__inner {
        color: #fff;
      }
    }
    .platform-list {
      width: 100%;
      display: flex;
      align-items: center;
      margin-top: 10px;
      .item {
        color: #acafac;
        &.active {
          background: #333;
          color: #10a37f;
        }
        &:before, &:after {
          background: #333;
        }
      }
    }
  }

}

html .theme-dark {
  .el-button {
    color: #333;
    background-color: #ccc;
  }
  .el-button--primary {
    color: #fff;
  }
  .el-tag--info {
    background: #333 !important;
    color: #acafac !important;
    border-color: #333 !important;
  }
  .hljs {
    color: #acafac;
  }
}
