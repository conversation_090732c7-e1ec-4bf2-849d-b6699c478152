.theme-light {

  background: #eff0f0;

  .sidebar-container {
    .module-list {
      .module-item {
        color: #606468;
        &:hover, &.active {
          color: #10a37f;
        }
      }
    }
  }

  .sub-sidebar {
    background: #f7f7f8;
  }

  .module-chat {
    .sub-sidebar {
      .group-chat {
        .module-title {
          color: #606468;
        }
        .group-item {
          &.active, &:hover {
            background: #eff0f0;
          }
          .group-title {
            color: #222;
          }
          .btn-dropdown{
            background: #eff0f0;
          }
        }
      }
    }
  }

  .module-write {
    .sub-sidebar {
      .category-item {
        &.primary-category {
          color: #333;

          .expand-icon i {
            color: #999;
          }

          &:hover .expand-icon i {
            color: #10a37f;
          }

          &.active .expand-icon i {
            color: #10a37f;
          }
        }

        &.sub-category {
          color: #666;
        }

        &:hover {
          background: #e8f5e8;
          color: #10a37f;
        }

        &.active {
          background: #e8f5e8;
          color: #10a37f;
        }
      }
    }

    .content-area-external {
      background: #fff;

      .content-header {
        border-bottom-color: #f0f0f0;

        .selected-category-title {
          color: #333;
        }

        .prompt-count {
          color: #999;
        }

        .close-btn {
          color: #666;

          &:hover {
            background: #f0f0f0;
            color: #333;
          }
        }
      }

      .prompt-item {
        background: #f9f9f9;
        border-color: #f9f9f9;

        &:hover {
          border-color: #e0e0e0;
        }

        &.active {
          border-color: #10a37f;
        }

        .prompt-title {
          color: #333;
        }

        .prompt-desc {
          color: #666;
        }

        .prompt-ops {
          .op-item {
            color: #999;

            &:hover {
              color: #10a37f;
            }
          }
        }
      }

      .empty-state {
        .empty-text {
          color: #666;
        }
      }
    }
  }

  .module-cosplay {
    .sub-sidebar {
      .group-cosplay {
        .topic-title {
          color: #606468;

          span {
            border-color: #10a37f;
          }
        }

        .role-item {
          background: #eff0f0;
          border-color: #eff0f0;

          &.active {
            border-color: #10a37f;
          }

          .role-title {
            color: #222222;
          }

          .role-ops {
            .op-item {
              color: #8e8ea0;
            }
          }
        }
      }
    }
  }

  .module-apps {
    .sub-sidebar {
      .app-list {
        .topic-title {
          color: #606468;

          span {
            border-color: #10a37f;
          }
        }

        .app-item {
          background: #eff0f0;
          border-color: #eff0f0;

          &.active {
            border-color: #10a37f;
          }

          .thumb {
            color: #303336;
          }

          .title {
            color: #222222;
          }
        }
      }
    }

    .main-batch {
      background: #f7f7f8;
      .batch-list {
        .batch-item {
          background: #eff0f0;
          border: 1px solid #eee;
          &:hover {
            background: #e8eaea;
          }
          .header {
            .count {
              color: #333;
            }
          }
          .body {
            color: #444;
          }
          .footer {
            .time {
              color: #999;
            }
          }
        }
        .btn-add {
          color: #666;
        }
      }

      .box-task {
        .task-main {
          .task-list {
            .task-item {
              border-top: 1px solid #e9e9e9;
              &:hover, &.active {
                background: #eff0f0;
              }
              .title {
                color: #444;
              }
            }
          }

          .task-chat {
            background: #eff0f0;
            border: 1px solid #e9e9e9;
          }
        }

        .task-empty {
          border-top: 1px solid #e9e9e9;
          .box-empty {
            width: 200px;
            .icon {
              color: #ddd;
            }
            .tips {
              color: #777;
            }
          }
        }
      }

    }

  }

  .main-container {
    .main-wrapper {
      .tab-ai {
        background: #f7f7f8;

        .tab-item {
          border: 1px solid #eff1f1;
          color: #606468;

          &:hover, &.active {
            color: #10a37f;
            border: 1px solid #10a37f;
          }
        }
      }

      .box-msg-list {
        .list {
          .text {
            color: #606468;
          }
        }
      }

      .style-chat {
        .list {
          .row-user {
            background: none;

            .message {
              .text {
                background: #10a37f;
                color: #fff;
              }

              .markdown-body {
                .code-linenum-line {
                  color: #ddd;
                }

                .code-block-header {
                  color: #ddd;
                }

                pre {
                  background: rgba(230, 231, 233, 0.4);

                  .hljs-comment {
                    color: #ddd;
                  }
                }
              }
            }
          }

          .row-ai {
            background: none;

            .message {
              .text {
                background: #f7f7f8;
                color: #606468;
              }
            }
          }
        }
      }

      .style-write {
        .list {
          .row-user {
            background: #f7f7f8;
            border-bottom: 1px solid #eff0f0;
          }

          .row-ai {
            background: #f7f7f8;
          }
        }

      }

      .box-input {
        //background: #eff0f0;
        .input {
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);

          .el-textarea__inner {
            background: #f7f7f8;
            color: #72787e;

            &::-webkit-input-placeholder {
              color: #72787e;
            }

            &:focus {
              border-color: #10a37f;
            }
          }
        }

        .btn-send {
          background: #f7f7f8 !important;
          color: #72787e;

          &:hover {
            color: #10a37f;
          }
        }
      }

      .markdown-body .highlight pre, .markdown-body pre {
        background: #e6e7e9;
      }

      .welcome {
        .title {
          color: #606468;
        }

        .h-item {
          color: #606468;
        }

        .tips-list {
          .column {
            li {
              background: #f7f7f8;
              color: #606468;

              &:active {
                background: #eff0f0;
              }
            }
          }
        }
      }

      .tab-ai {
        .tab-item.gold {
          &.active, &:hover {
            border: 1px solid #f5ad0c !important;
            color: #f5ad0c !important;
          }
        }
      }
      .gold {
        .el-textarea__inner:focus {
          border: 1px solid #ffa41a !important;
        }
      }

      .pages {
        span {
          border: 1px solid #eee;
          color: #666;
          &.active, &:hover {
            background-color: #e3f5f0;
            color: #10a37f;
          }
          &.active {
            border-color: #10a37f;
          }
        }
      }

    }
  }

  .float {
    .btn {
      background: #fff;
      color: #606468;
      &:hover {
        border-color: #10a37f;
        color: #10a37f;
      }
    }
  }
}
