.module-video {
  .video-setting {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    border-radius: 10px;
    overflow: hidden;
    transition: background-color 0.1s ease-in-out;

    .module-body {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 80px;
      padding: 10px 0;

      .scrollbar-wrapper {
        overflow-x: hidden !important;
      }

      .el-scrollbar__bar.is-vertical {
        right: 0;
        width: 3px;
      }

      .el-scrollbar {
        height: 100%;
      }

      .box-video-setting {
        padding: 10px 20px;
        box-sizing: border-box;
        .uploader {
          border: 1px solid #DCDFE6;
        }
        .setting-row {
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;
          .setting-item {
            width: 100%;
            padding: 10px 20px 20px 20px;
            background: #eff0f0;
            border-radius: 10px;
            &.col-1 {
              width: 220px;
            }
            &.col-2 {
              width: 350px;
            }
            &.col-3 {
              width: 480px;
            }
          }

          .header {
            font-size: 16px;
            color: #333;
            margin-bottom: 20px;
            margin-top: 15px;
            vertical-align: middle;
            letter-spacing: 1px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .tips {
              color: #999;
            }
          }
          .options {
            width: 100%;
            .option-item {
              width: 76px;
              height: 76px;
              float: left;
              margin-bottom: 8px;
              margin-right: 8px;
              border-radius: 10px;
              display: flex;
              align-items: center;
              justify-content: center;
              box-sizing: border-box;
              position: relative;
              overflow: hidden;
              cursor: pointer;
              transition: all .2s;
              background-clip: padding-box, border-box;
              background-origin: padding-box, border-box;
              border: 3px solid transparent;
              &.active {
                background-image: linear-gradient(to right,#333333, #333333),linear-gradient(270deg,#FF5CE4 0%, #36C2FF 100%);
              }
              img {
                width: 100%;
                height: 100%;
              }
              span {
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 36px;
                line-height: 44px;
                white-space: nowrap;
                text-align: center;
                z-index: 9;
                color: #fff;
                font-size: 12px;
                font-weight: 500;
                background: linear-gradient(180deg, rgba(0,0,0,0) 0%, #000000 100%)
              }
            }

            textarea {
              transition: all 0.1s;
              &:focus {
                border-color: #10a37f !important;
              }
            }
            .el-upload {
              transition: all 0.1s;
              background: #fff;
              &:hover {
                border-color: #10a37f !important;
                .avatar-uploader-icon {
                  color: #10a37f !important;
                }
              }
            }
          }

          .image {
            display: block;
            height: 80px;
            width: auto;
            min-width: 80px;
            max-width: 180px;
            position: relative;
            text-align: center;
            img {
              height: 80px;
              width: auto;
            }
            .del {
              display: block;
              position: absolute;
              right: 0;
              top: 0;
              background: rgba(0, 0, 0, 0.5);
              color: #fff;
              font-size: 12px;
              cursor: pointer;
              width: 24px;
              height: 24px;
              text-align: center;
              line-height: 24px;
              border-bottom-left-radius: 6px;
              &:hover {
                background: #000;
              }
            }
          }

        }

        .size-item {
          width: 76px;
          height: 100px;
          float: left;
          margin-right: 10px;
          border-radius: 10px;
          box-sizing: border-box;
          position: relative;
          overflow: hidden;
          cursor: pointer;
          text-align: center;
          transition: all .2s;
          background-clip: padding-box, border-box;
          background-origin: padding-box, border-box;
          border: 2px solid #e2e2e2;
          padding: 10px;
          background-color: #fff;
          span {
            position: absolute;
            left:0;
            bottom: 0;
            display: block;
            width: 100%;
            height: 32px;
            line-height: 32px;
            color: #333;
            font-size: 14px;
          }

          .size-block {
            margin: 0 auto;
            background: #ddd;
            border-radius: 4px;
            transition: all .2s;
            &.size0 {
              width: 52px;
              height: 39px;
            }
            &.size1 {
              width: 39px;
              height: 52px;
            }
            &.size2 {
              width: 52px;
              height: 52px;
            }
            &.size3 {
              width: 52px;
              height: 29px;
            }
            &.size4 {
              width: 29px;
              height: 52px;
            }
          }
          &.active {
            border-color: #10a37f;
            .size-block {
              background: #10a37f;
            }
            span {
              color: #10a37f;
            }
          }
          &:last-child {
            margin-right: 0;
          }

        }
      }

    }
    .module-footer {
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      height: 80px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      .box-price {
        min-width: 140px;
        height: 40px;
        background: linear-gradient(30deg, #f7debe, #f4c384);
        border-radius: 5px;
        margin: 0;
        padding: 5px 10px 5px 12px;
        box-sizing: border-box;
        color: #9a5b12;
        transition: opacity 0.1s ease-in-out;
        cursor: pointer;
        font-size: 13px;
        letter-spacing: 1px;
        display: flex;
        align-items: center;
        overflow: hidden;
        white-space: nowrap;

        &:hover {
          opacity: 0.8;
        }

        .icon {
          font-size: 18px;
          margin-right: 5px;
        }
        .num {
          font-size: 16px;
          font-weight: bold;
          margin: 0 5px;
        }
        .vip {
          font-size: 14px;
          font-weight: bold;
          margin: 0 5px;
        }
      }
    }
  }
  .size-big {
    .sub-sidebar {
      width: 755px;
    }
    .main-wrapper {
      left: 755px;
    }
    .video-setting {
      width: 100%;
      .module-footer {
        .box-wallet {
          width: 200px;
        }
      }
    }
    .wrapper {
      left: 755px;
    }
  }
  .size-small {
    .sub-sidebar {
      width: 500px;
    }
    .main-wrapper {
      left: 500px;
    }
    .video-setting {
      width: 100%;
      .box-video-setting {
        width: 100%;
      }
      .module-footer {
        .box-wallet {
          width: 200px;
        }
      }
    }
    .wrapper {
      left: 265px;
    }
  }

  .platform-list {
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 10px;
    .item {
      position: relative;
      height: 48px;
      line-height: 36px;
      padding: 0 25px;
      font-size: 16px;
      font-weight: bold;
      white-space: nowrap;
      cursor: pointer;
      transition: top 0.1s linear, background 0.2s linear, color 0.2s linear;
      background: none;
      top: 2px;
      color: #666;
      &.active {
        background: linear-gradient(#e3f5f0 0%, #f7f7f8 75%);
        height: 55px;
        line-height: 42px;
        top: 0;
        box-shadow: 0 -5px 20px 0 rgba(0,0,0,0.1);
        border-radius: 10px 10px 0 0;
        color: #10a37f;
      }
      &:before, &:after {
        content: '';
        display: none;
        width: 10px;
        height: 100%;
        background: linear-gradient(#e3f5f0 0%, #f7f7f8 75%);
        position: absolute;
        left: -5px;
        top: 0;
        z-index: 2;
        border-radius: 10px 10px 0 0;
        transform: skew(-10deg);
      }
      &:after {
        left: auto;
        right: -5px;
        top: 0;
        //border-top-left-radius: 0;
        transform: skew(10deg);
      }
      &.active:before, &.active:after{
        display: block;
      }
      &:first-child {
        border-top-left-radius: 10px;
        &.active {
          box-shadow: 6px -5px 16px 0 rgba(0,0,0,0.1);
        }
        &.active:before {
          display: none;
        }
      }
      &:last-child {
        border-top-right-radius: 10px;
      }
    }

  }

  .type-list {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    z-index: 999;
    .tab-type {
      height: 36px;
      border-radius: 5px;
      box-sizing: border-box;
      z-index: 99;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 0 10px rgb(0, 0, 0, 0.1);
      transition: background 0.1s ease-in-out;
      overflow: hidden;

      .tab-item {
        height: 36px;
        line-height: 36px;
        text-align: center;
        font-size: 15px;
        cursor: pointer;
        transition: all 0.1s ease-in-out;
        padding: 0 15px;
        background: #fff;
        color: #333;
        &.active {
          background: #10a37f;
          color: #fff;
        }
        &:first-child {
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
        }
        &:last-child {
          border-top-right-radius: 5px;
          border-bottom-right-radius: 5px;
        }
      }
    }
  }
  /** 作品广场列表 **/
  .video-list {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    margin-top: 70px;
    margin-bottom: 30px;
    //transition: all 0.1s linear;
    display: grid;
    gap: 12px;
    .video-item {
      display: flex;
      flex-direction: column;
      transition: width 0.2s linear;
      gap: 12px;
      overflow: hidden;
      box-sizing: border-box;
      //background: #f7f7f8;
      //border-radius: 5px;
      .video {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: calc(56.306%);
        background: #1f1f1f;
        text-align: center;
        border-radius: 5px;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        .poster {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background-size: cover;
          background-position: center center;
          background-repeat: no-repeat;
          z-index: 1;
          opacity: 1;
          transition: all 0.2s linear;
        }
        .btn-play {
          position: absolute;
          left: 0;
          top: 0;
          z-index: 2;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          opacity: 1;
          transition: bottom 0.1s linear;
          .icon {
            width: 60px;
            height: 43px;
            background-size: cover;
            background-repeat: no-repeat;
            opacity: 0.8;
          }
        }
        video {
          position: absolute;
          max-height: 100%;
          width: 100%;
          object-fit: contain;
          vertical-align: middle;
          left: 0;
          top: 50%;
          transform: translate(0, -50%);
          opacity: 0;
        }
        .prompt {
          position: absolute;
          width: 100%;
          height: 40px;
          line-height: 44px;
          padding: 0 10px;
          left: 0;
          bottom: -40px;
          z-index: 2;
          text-align: left;
          color: #fff;
          transition: bottom 0.1s linear;
          font-size: 13px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.2));
        }
        &.active {
          .poster {
            opacity: 0;
          }
          video {
            opacity: 1;
          }
          .prompt {
            bottom: 0;
          }
          .btn-play {
            opacity: 0;
          }
        }
      }

    }
  }


}
@media (min-width: 800px) {
  .module-video .video-list {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
@media (min-width: 1100px) {
  .module-video .video-list {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (min-width: 1300px) {
  .module-video .video-list {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

