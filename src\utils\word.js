import docxtemplater from 'docxtemplater';
import <PERSON>z<PERSON><PERSON> from 'pizzip';
import JSZipUtils from 'jszip-utils'
import { saveAs } from 'file-saver';
import JSZip from 'jszip';

// 导出方法
export const exportDocx = (tempDocxPath, list, zipName) => {
  const promises = [];
  const zips = new JSZip();
  //  循环数据，生成word文件
  list.forEach((element, index) => {
    let fileName = zipName + '（' + index + '）' + '.docx'; // word文件名称
    let data = element;
    const promise = new Promise((resolver, reject) => {
      JSZipUtils.getBinaryContent(tempDocxPath, (error, content) => {
        if (error) {
          throw error;
        }
        let zip = new PizZip(content);
        let doc = new docxtemplater().loadZip(zip);
        doc.setData(data);
        try {
          doc.render();
        } catch (error) {
          throw error;
        }
        let out = doc.getZip().generate({
          type: 'blob',
          mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });
        // 添至zip集合中
        zips.file(fileName, out, { binary: true });
        resolver();
      })
    })
    promises.push(promise);
  })

  // 下载zip包
  Promise.all(promises).then(() => {
    zips.generateAsync({ type: 'blob' }).then((content) => {
      saveAs(content, zipName);
    })
  })
}
