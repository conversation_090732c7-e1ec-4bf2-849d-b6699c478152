<template>
  <div class="box-input">
    <div class="input">
      <el-input
        ref="messageInput"
        v-model="message"
        placeholder="告诉AI你想写什么..."
        type="textarea"
        :autofocus="true"
        :autosize="{ minRows: 1, maxRows: 8 }"
        @keyup.enter.native="onEnter"
      />
      <el-button
        v-if="writing"
        class="btn-send btn-stop"
        type="default"
        :title="'生成中，点击停止' | lang"
        @click="stopFetch"
      >
        <svg-icon icon-class="ic_stop"></svg-icon> 停止生成
      </el-button>
      <el-button
        v-else
        class="btn-send"
        type="default"
        :loading="writing"
        :title="'发送' | lang"
        @click="sendText"
      >
        <i v-if="!writing" class="el-icon-position" /> 发送
      </el-button>
    </div>
  </div>
</template>

<script>
import { getSiteCode, getToken } from '@/utils/auth'
var textStacks = []
var textOutputSi = 0
var fetchCtrl = null

export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      message: '',
      writing: false,
      writingText: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.messageInput.focus()
    })
  },
  methods: {
    async sendText() {
      if (this.writing || this.writingText) {
        return
      }
      const message = this.message
      if (!message) {
        this.$message.error('请输入内容')
        return
      }

      if (textOutputSi) {
        clearInterval(textOutputSi)
        textOutputSi = 0
        textStacks = []
      }

      this.writing = true
      this.clearAllNodes()
      this.minder.disableAnimation()
      this.message = ''

      const headers = new Headers()
      headers.append('Content-Type', 'application/json')
      headers.append('X-token', getToken() || '')
      headers.append('X-site', getSiteCode() || '')
      const url = process.env.VUE_APP_BASE_API + '/chat/sendText'
      const data = {
        tool: 'mind',
        message: message
      }
      fetchCtrl = new AbortController()
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
        signal: fetchCtrl.signal
      })
      if (!response.ok) {
        this.writing = false
        this.$message.error(response.statusText)
        return
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let done = false
      const curAiMsg = ''

      // eslint-disable-next-line require-atomic-updates
      textOutputSi = setInterval(() => {
        if (this.writing) {
          if (textStacks.length > 0) {
            this.writingText += textStacks.join('')
            textStacks = []
            this.importMarkdown(this.writingText)
          }
        } else {
          if (textStacks.length > 0) {
            this.writingText += textStacks.join('')
            textStacks = []
          }
          clearInterval(textOutputSi)
          textOutputSi = 0
          this.importMarkdown(this.writingText)
          this.writingText = ''
          this.minder.enableAnimation()
          setTimeout(() => {
            if (this.minder._root.getChildren().length === 0) {
              this.minder._root.setData('text', '中心主题')
              this.minder.refresh()
              this.$message.error('生成失败')
            } else {
              this.$message.success('生成结束')
            }
          }, 500)
        }
      }, 50)

      while (!done) {
        const { value, done: readerDone } = await reader.read()
        if (value) {
          const char = decoder.decode(value)
          if (char === '\n' && curAiMsg.endsWith('\n')) {
            continue
          }
          if (char) {
            if (char.indexOf('[error]') !== -1) {
              this.writing = false
              this.message = message
              const error = char.replace('[error]', '')
              if (error.indexOf('请登录') !== -1) {
                this.$emit('showLogin')
                setTimeout(() => {
                  this.$message.error(this.$lang(error))
                }, 500)
              } else if (error.indexOf('请充值') !== -1) {
                this.$emit('showPay', 'vip')
                setTimeout(() => {
                  this.$message.error(this.$lang(error))
                }, 500)
              } else {
                this.$alert(error, '系统提示')
              }
              break
            }
            this.writing = true
            for (var i = 0; i < char.length; i++) {
              textStacks.push(char[i])
            }
          }
        }
        done = readerDone
      }
      this.writing = false
    },
    onEnter(e) {
      if (e.ctrlKey) {
        this.sendText()
      }
      return false
    },
    stopFetch() {
      this.writing = false
      fetchCtrl.abort()
    },
    trim(str) {
      if (str) {
        str = str.replace(/(^\s*)|(\s*$)/g, '')
      }
      return str
    },
    // 导入markdown到minder
    importMarkdown(content) {
      content = this.trim(content)
      if (!content) {
        return
      }
      try {
        this.minder.decodeData('markdown', content).then(json => {
          if (!json.data.text) {
            return
          }
          this.clearAllNodes()
          this.importNode(this.minder._root, json)
        })
      } catch (e) {
        // this.writingText = content.substring(1)
      }
    },
    // 导入所有节点，仅同步差异
    importNode(node, json) {
      for (var field in json.data) {
        if (node.data.text !== json.data.text) {
          node.setData(field, json.data[field])
        }
      }
      var nodeChildren = node.getChildren() || []
      var jsonChildren = json.children || []

      for (var i = 0; i < jsonChildren.length; i++) {
        if (nodeChildren[i]) {
          for (var field2 in jsonChildren[i].data) {
            if (nodeChildren[i].data[field2] !== jsonChildren[i].data[field2]) {
              nodeChildren[i].setData(field2, jsonChildren[i].data[field2])
              this.minder.refresh()
            }
          }
          this.importNode(nodeChildren[i], jsonChildren[i])
        } else {
          if (jsonChildren[i].data.text) {
            const childNode = this.minder.createNode(jsonChildren[i].data, node)
            this.minder.refresh()
            this.importNode(childNode, jsonChildren[i])
          }
        }
      }
    },
    // 删除当前所有节点
    clearAllNodes() {
      while (this.minder._root.getChildren().length) {
        this.minder.removeNode(this.minder._root.getChildren()[0])
      }
      this.minder._root.setData('text', '生成中...')
      this.minder.refresh()
    }
  }
}
</script>
<style lang="scss" scoped>
.fox-mind {
  .box-input {
    z-index: 3;
    .input {
      padding-bottom: 0;
      margin-bottom: 10px;
      .btn-stop {
        color: #10a37f;
      }
    }
  }
}
</style>
