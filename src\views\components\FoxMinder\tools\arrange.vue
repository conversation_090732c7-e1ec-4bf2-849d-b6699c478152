<template>
  <div class="km-btn-group">
    <el-row>
      <el-link :disabled="isDisabled('ArrangeUp')" :underline="false" type="primary" @click="minder.execCommand('ArrangeUp')"><i class="el-icon-arrow-up" /> 上移</el-link>
    </el-row>

    <el-row>
      <el-link :disabled="isDisabled('ArrangeDown')" :underline="false" type="primary" @click="minder.execCommand('ArrangeDown')"><i class="el-icon-arrow-down" /> 下移</el-link>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  methods: {
    isDisabled: function(command) {
      return this.minder.queryCommandState(command) === -1
    }
  }
}
</script>
