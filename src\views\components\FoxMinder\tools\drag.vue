<template>
  <div class="tools-item" :class="{active: minder.queryCommandState('hand') === 1}" @click="minder.execCommand('hand')">
    <el-tooltip content="拖动模式">
      <a><svg-icon class="icon" icon-class="ic_mind_drag" /></a>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    editor: {
      type: Object,
      default: null
    },
    minder: {
      type: Object,
      default: null
    }
  },
  mounted() {

  }
}
</script>
