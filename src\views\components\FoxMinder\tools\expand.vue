<template>
  <el-dropdown
    placement="bottom"
    trigger="click"
    @command="handleExpand"
  >
    <div class="tools-item">
      <span class="el-dropdown-link">
        <el-tooltip content="展开节点">
          <a>展开</a>
        </el-tooltip>
      </span>
      <el-dropdown-menu slot="dropdown">
        <div class="option-list">
          <el-dropdown-item v-for="(item, index) in expandItems" :command="index">
            <div class="option-item">{{ item }}</div>
          </el-dropdown-item>
        </div>
      </el-dropdown-menu>
    </div>
  </el-dropdown>
</template>

<script>
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      expandItems: ['全部展开', '展开到 1 层节点', '展开到 2 层节点', '展开到 3 层节点', '展开到 4 层节点', '展开到 5 层节点', '展开到 6 层节点']
    }
  },
  methods: {
    handleExpand(level) {
      this.minder.execCommand('ExpandToLevel', level || 999)
    },
    handleSelected(key) {
      this.selectNode(key)
    }
  }
}
</script>
