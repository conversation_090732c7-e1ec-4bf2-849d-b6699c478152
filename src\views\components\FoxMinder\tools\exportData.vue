<template>
  <el-dropdown
    placement="bottom"
    trigger="click"
    @command="handleExport"
  >
    <div class="tools-item">
      <div class="el-dropdown-link">
        <div style="display: flex; align-items: center;">
          <svg-icon class="icon" icon-class="ic_mind_export" />
          <a style="margin-left: 5px;">导出</a>
        </div>
      </div>
      <el-dropdown-menu slot="dropdown">
        <div class="option-list">
          <el-dropdown-item v-for="(item, index) in types" :command="index">
            <div class="option-item">{{ item.title }}</div>
          </el-dropdown-item>
        </div>
      </el-dropdown-menu>
    </div>
  </el-dropdown>
</template>

<script>
import fileSaver from 'file-saver'
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      types: [{
        name: 'png',
        title: 'PNG图片（.png）',
        ext: 'png'
      }, {
        name: 'svg',
        title: 'SVG矢量图（.svg）',
        ext: 'svg'
      }, {
        name: 'text',
        title: '文本文件（.txt）',
        ext: 'txt'
      }, {
        name: 'markdown',
        title: 'Markdown文件（.md）',
        ext: 'md'
      }, {
        name: 'json',
        title: 'JSON格式（.json）',
        ext: 'json'
      }]
    }
  },
  methods: {
    handleExport(index) {
      const item = this.types[index]
      this.minder.exportData(item.name).then(content => {
        let contentBlob = ''
        if (item.name === 'png') {
          contentBlob = content
        } else {
          if (item.name === 'json') {
            content = content.replace(',"version":"1.4.43"', '')
          }
          contentBlob = new Blob([content], { type: 'text/plain;charset=utf-8' })
        }
        fileSaver.saveAs(contentBlob, '思维导图.' + item.ext)
      })
    }
  }
}
</script>
