<template>

  <div class="km-btn-group">
    <el-row>
      <el-link :disabled="isDisabled('note')" :underline="false" type="primary" @click="noteVisible = true"><i class="el-icon-edit-outline" /> 备注</el-link>
    </el-row>

    <el-row>
      <el-link :disabled="isDisabled('RemoveNode')" :underline="false" type="primary" @click="minder.execCommand('RemoveNode')"><i class="el-icon-delete" /> 删除</el-link>
    </el-row>

    <el-drawer
      title="编辑备注"
      :visible.sync="noteVisible"
    >
      <div style="margin: 0 20px">
        <el-input v-model="note" rows="10" type="textarea" />
      </div>
    </el-drawer>
  </div>

</template>

<script>
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },

  data() {
    return {
      noteVisible: false,
      note: this.minder.queryCommandValue('note')
    }
  },
  watch: {
    note: function() {
      this.minder.execCommand('note', this.note)
    }
  },
  updated() {
    this.note = this.minder.queryCommandValue('note')
  },
  methods: {
    isDisabled: function(command) {
      return this.minder.queryCommandState(command) === -1
    }
  }
}
</script>
