<template>
  <el-dropdown placement="bottom" @command="handleSelected">
    <div class="tools-item">
      <span class="el-dropdown-link">
        <el-tooltip content="选择节点">
          <a>选择</a>
        </el-tooltip>
      </span>
      <el-dropdown-menu slot="dropdown">
        <div class="option-list">
          <el-dropdown-item v-for="(item, index) in Object.keys(selectedItems)" :command="index">
            <div class="option-item">{{ selectedItems[item] }}</div>
          </el-dropdown-item>
        </div>
      </el-dropdown-menu>
    </div>
  </el-dropdown>
</template>

<script>
export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selectedItems: {
        'selectall': '全选',
        'selectrevert': '反选',
        'selectlevel': '选中同级',
        'selecttree': '选择子树'
      }
    }
  },
  methods: {
    handleSelected(index) {
      const key = Object.keys(this.selectedItems)[index]
      this.selectNode(key)
    },
    selectNode(key) {
      if (this.minder && key) {
        const minder = this.minder
        const methods = {
          selectall: function() {
            const selection = []
            minder.getRoot().traverse(function(node) {
              selection.push(node)
            })
            minder.select(selection, true)
            minder.fire('receiverfocus')
          },
          selectrevert: function() {
            const selected = minder.getSelectedNodes()
            const selection = []
            minder.getRoot().traverse(function(node) {
              if (selected.indexOf(node) === -1) {
                selection.push(node)
              }
            })
            minder.select(selection, true)
            minder.fire('receiverfocus')
          },
          selectsiblings: function() {
            const selected = minder.getSelectedNodes()
            const selection = []
            selected.forEach(function(node) {
              if (!node.parent) return
              node.parent.children.forEach(function(sibling) {
                if (selection.indexOf(sibling) === -1) selection.push(sibling)
              })
            })
            minder.select(selection, true)
            minder.fire('receiverfocus')
          },
          selectlevel: function() {
            const selectedLevel = minder.getSelectedNodes().map(function(node) {
              return node.getLevel()
            })
            const selection = []
            minder.getRoot().traverse(function(node) {
              if (selectedLevel.indexOf(node.getLevel()) !== -1) {
                selection.push(node)
              }
            })
            minder.select(selection, true)
            minder.fire('receiverfocus')
          },
          selectpath: function() {
            const selected = minder.getSelectedNodes()
            const selection = []
            selected.forEach(function(node) {
              while (node && selection.indexOf(node) === -1) {
                selection.push(node)
                node = node.parent
              }
            })
            minder.select(selection, true)
            minder.fire('receiverfocus')
          },
          selecttree: function() {
            const selected = minder.getSelectedNodes()
            const selection = []
            selected.forEach(function(parent) {
              parent.traverse(function(node) {
                if (selection.indexOf(node) === -1) selection.push(node)
              })
            })
            minder.select(selection, true)
            minder.fire('receiverfocus')
          }
        }

        methods[key]()
      }
    }
  }
}
</script>
