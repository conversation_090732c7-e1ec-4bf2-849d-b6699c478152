<template>
  <el-dropdown
    placement="bottom"
    trigger="click"
    @command="handleCommand"
  >
    <div class="tools-item">
      <span class="el-dropdown-link">
        <el-tooltip content="类型">
          <a>
            <svg-icon class="icon" :icon-class="'ic_mind_tpl_' + curName" />
          </a>
        </el-tooltip>
      </span>
      <el-dropdown-menu slot="dropdown">
        <div class="tpl-list">
          <div v-for="name in Object.keys(templateList)">
            <el-dropdown-item :command="name">
              <el-tooltip :content="titles[name]" placement="right">
                <div class="tpl-item" :class="{active: curName === name}"><svg-icon :icon-class="'ic_mind_tpl_' + name" /></div>
              </el-tooltip>
            </el-dropdown-item>
          </div>
        </div>
      </el-dropdown-menu>
    </div>
  </el-dropdown>
</template>

<script>

export default {
  props: {
    minder: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      templateList: window.kityminder.Minder.getTemplateList(),
      curName: 'right',
      titles: {
        'default': '思维导图',
        'structure': '组织结构图',
        'filetree': '目录组织图',
        'right': '逻辑结构图 - 右',
        'fish-bone': '鱼骨图',
        'tianpan': '天盘图'
      }
    }
  },
  mounted() {
    this.curName = this.minder.queryCommandValue('template') || 'default'
  },
  methods: {
    handleCommand(command) {
      this.curName = command
      this.minder.execCommand('template', command)
    }
  }
}
</script>
<style lang="scss" scoped>
.tpl-list {
  width: 80px;
  text-align: center;
  .tpl-item {
    width: 100%;
    font-size: 32px;
    padding: 10px 0;
    color: #666;
    &.active {
      color: #10a37f;
    }
  }
}
</style>
