<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      title=""
      :visible="true"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      :show-close="true"
      :before-close="closeLogin"
    >
      <div class="container" :style="'left: ' + containerLeft + 'px;'">
        <div class="reg-container">
          <div class="title-container">{{ '注册账号' | lang }}</div>
          <el-form ref="regForm" :model="regForm" class="form" auto-complete="on" label-position="left">
            <el-form-item prop="phone">
              <span class="svg-container">
                <svg-icon icon-class="ic_login_phone" style="font-size: 20px; position: relative; left: -2px;" />
              </span>
              <el-input
                ref="regPhone"
                v-model="regForm.phone"
                :placeholder="'手机号' | lang"
                type="text"
                auto-complete="on"
              />
            </el-form-item>

            <el-form-item prop="code" >
              <span class="svg-container">
                <svg-icon icon-class="ic_login_code" style="font-size: 20px; position: relative; left: -2px;" />
              </span>
              <el-input
                ref="regCode"
                v-model="regForm.code"
                type="text"
                :placeholder="'短信验证码' | lang"
                maxlength="6"
              />
              <el-button type="text" class="sendcode" size="small" @click="doSendSms('reg')" :disabled="sendSmsCountdown > 0">
                {{ sendSmsCountdown > 0 ? `${sendSmsCountdown}s ` + $lang('后可重发') : $lang('发送验证码') }}
              </el-button>
            </el-form-item>

            <el-form-item prop="password">
              <span class="svg-container">
                <svg-icon icon-class="ic_login_pwd" style="font-size: 16px;" />
              </span>
              <el-input
                :key="passwordType"
                ref="regPassword"
                v-model="regForm.password"
                :type="passwordType"
                :placeholder="'登录密码' | lang"
                @keyup.native.enter="doReg"
              />
              <span class="show-pwd" @click="showPwd">
                <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
              </span>
            </el-form-item>

            <div style="margin-top: 30px;">
              <el-button :loading="loading" type="primary" style="width:170px;" @click.native.prevent="doReg">{{ '立即注册' | lang }}</el-button>
              <el-button :loading="loading" type="default" style="width:100px;" @click.native.prevent="switchFormType('login')">{{ '去登录' | lang }}</el-button>
            </div>

            <div class="agree">
              {{ '注册即表示同意' | lang }}<a @click="toDoc('service')">{{ '《服务协议》' | lang }}</a>{{ '和' | lang }}<a @click="toDoc('privacy')">{{ '《隐私政策》' | lang }}</a>
            </div>

          </el-form>
        </div>
        <div class="login-container">
          <div class="login-type" v-if="login_wechat && login_phone">
            <div class="item" :class="{active: loginType === 'wechat'}" @click="switchLoginType('wechat')">
              {{ '微信登录' | lang }}
              <span></span>
            </div>
            <div class="item" :class="{active: loginType === 'phone'}" @click="switchLoginType('phone')">
              {{ '账号登录' | lang }}
              <span></span>
            </div>
          </div>
          <div v-else>
            <div class="title-container" style="margin-top: 10px;">
              <span v-if="login_wechat">{{ '微信登录' | lang }}</span>
              <span v-if="login_phone">{{ '账号登录' | lang }}</span>
            </div>
          </div>
          <el-row v-if="loginType === 'wechat'" style="text-align: center;">
            <div class="qrcode">
              <img :src="loginQrcode" v-if="loginQrcode" />
            </div>
            <div class="tips">
              <svg-icon class="icon" icon-class="ic_wechat" style="color: #10a37f; margin-right: 5px;" />
              <span>{{ '使用微信扫码' | lang }}</span>
            </div>
          </el-row>
          <el-form v-if="loginType === 'phone'" ref="loginForm" :model="loginForm" class="form" auto-complete="on" label-position="left">
            <el-form-item prop="phone">
              <span class="svg-container">
                <svg-icon icon-class="ic_login_phone" style="font-size: 20px; position: relative; left: -2px;" />
              </span>
              <el-input
                ref="loginPhone"
                v-model="loginForm.phone"
                :placeholder="'手机号' | lang"
                type="text"
                auto-complete="on"
              />
            </el-form-item>

            <el-form-item prop="password">
            <span class="svg-container">
              <svg-icon icon-class="ic_login_pwd" style="font-size: 16px;" />
            </span>
              <el-input
                :key="passwordType"
                ref="loginPassword"
                v-model="loginForm.password"
                :type="passwordType"
                :placeholder="'登录密码' | lang"
                auto-complete="on"
                @keyup.native.enter="doLogin"
              />
              <span class="show-pwd" @click="showPwd">
              <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span>
            </el-form-item>

            <el-button :loading="loading" type="primary" style="width:100%; margin-bottom:10px; margin-top: 20px;" @click.native.prevent="doLogin">{{ '确定登录' | lang }}</el-button>
            <div class="navs">
              <el-button type="text" class="btn" @click="switchFormType('reg')">{{ '注册账号' | lang }}</el-button>
              <el-button type="text" class="btn" @click="switchFormType('reset')">{{ '忘记密码' | lang }}</el-button>
            </div>

          </el-form>
        </div>
        <div class="reset-container">
          <div class="title-container">{{ '修改密码' | lang }}</div>
          <el-form ref="resetForm" :model="resetForm" class="form" auto-complete="on" label-position="left">
            <el-form-item prop="phone">
              <span class="svg-container">
                <svg-icon icon-class="ic_login_phone" style="font-size: 20px; position: relative; left: -2px;" />
              </span>
              <el-input
                ref="resetPhone"
                v-model="resetForm.phone"
                :placeholder="'手机号' | lang"
                type="text"
                auto-complete="on"
              />
            </el-form-item>

            <el-form-item prop="code" >
              <span class="svg-container">
                <svg-icon icon-class="ic_login_code" style="font-size: 20px; position: relative; left: -2px;" />
              </span>
              <el-input
                ref="resetCode"
                v-model="resetForm.code"
                type="text"
                :placeholder="'短信验证码' | lang"
                maxlength="6"
              />
              <el-button type="text" class="sendcode" size="small" @click="doSendSms('reset')" :disabled="sendSmsCountdown > 0">
                {{ sendSmsCountdown > 0 ? `${sendSmsCountdown}s ` + $lang('后可重发') : $lang('发送验证码') }}
              </el-button>
            </el-form-item>

            <el-form-item prop="password">
              <span class="svg-container">
                <svg-icon icon-class="ic_login_pwd" style="font-size: 16px;" />
              </span>
              <el-input
                :key="passwordType"
                ref="resetPassword"
                v-model="resetForm.password"
                :type="passwordType"
                :placeholder="'新登录密码' | lang"
                @keyup.native.enter="doReset"
              />
              <span class="show-pwd" @click="showPwd">
              <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span>
            </el-form-item>

            <el-button :loading="loading" type="primary" style="width:100%; margin-bottom:10px; margin-top: 20px;" @click.native.prevent="doReset">{{ '修改密码' | lang }}</el-button>
            <div class="navs">
              <el-button type="text" class="btn" @click="switchFormType('login')">{{ '返回登录' | lang }}</el-button>
            </div>
          </el-form>
        </div>
      </div>
      <Sendsms v-if="sendSmsShow" :phone="sendSmsPhone" :type="sendSmsType" @success="sendSmsSuccess" @close="sendSmsClose" />

    </el-dialog>
  </div>
</template>

<script>
import { getQrcode, loginCheck, login, reg, reset } from '@/api/login'
import { setToken, getStorage, setStorage } from '@/utils/auth'
import { toDoc } from '@/utils/util'
import Sendsms from './Sendsms'

export default {
  name: 'Login',
  components: { Sendsms },
  data() {
    return {
      formType: 'login',
      loginType: '',
      loginQrcode: '',
      loginSi: 0,
      sendSmsType: '', // reg or reset
      sendSmsPhone: '',
      sendSmsShow: false,
      sendSmsCountdown: 0,
      loginForm: {
        phone: '',
        password: ''
      },
      regForm: {
        phone: '',
        password: '',
        code: ''
      },
      resetForm: {
        phone: '',
        password: '',
        code: ''
      },
      loading: false,
      passwordType: 'password'
    }
  },
  computed: {
    containerLeft() {
      if (this.formType === 'reg') {
        return 0;
      } else if (this.formType === 'login') {
        return -360;
      } else if (this.formType === 'reset') {
        return -720;
      }
    },
    login_phone() {
      return this.$store.state.user.login_phone
    },
    login_wechat() {
      return this.$store.state.user.login_wechat
    }
  },
  watch: {
    loginType() {
      if (this.loginType === 'phone') {
        this.fillDefaultPhone()
      }
    }
  },
  created() {
    var si = setInterval(() => {
      if (this.login_wechat !== '') {
        clearInterval(si);
        if (this.login_wechat === 0 && this.login_phone === 0) {
          this.$message.error('请先配置登录方式')
        } else {
          if (this.login_wechat) {
            this.loginType = 'wechat'
            this.getLoginQrcode()
          } else {
            this.loginType = 'phone'
          }
        }
      }
    }, 100)
  },
  methods: {
    closeLogin() {
      if (this.loginSi) {
        clearInterval(this.loginSi)
      }
      this.$emit('close')
    },
    fillDefaultPhone() {
      this.$nextTick(() => {
        let phone = getStorage('phone')
        if (phone) {
          this.loginForm.phone = phone
          this.$refs.loginPassword.focus()
        } else {
          this.$refs.loginPhone.focus()
        }
      })
    },
    switchFormType(type) {
      this.formType = type
      if (type === 'reset') {
        this.resetForm = {
          phone: this.loginForm.phone,
          code: '',
          password: ''
        }
      } else if (type === 'login') {
        setTimeout(() => {
          this.fillDefaultPhone()
        }, 500)
      }
    },
    switchLoginType(type) {
      this.loginType = type
      if (type === 'wechat') {
        this.getLoginQrcode()
      } else {
        clearInterval(this.loginSi)
        this.fillDefaultPhone()
      }
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
    },
    getLoginQrcode() {
      getQrcode({
        type: 'login'
      }).then(res => {
        this.loginQrcode = res.data.qrcode
        if (this.loginSi) {
          clearInterval(this.loginSi)
        }
        this.loginSi = setInterval(() => {
          loginCheck({
            code: res.data.code
          }).then(res => {
            if (res.data.login) {
              this.$message.success(res.message)
              setToken(res.data.token)
              window.location.reload()
            }
          })
        }, 3000);
      })
    },
    doSendSms(type) {
      var phone = ''
      if (type === 'reg') {
        phone = this.regForm.phone
      } else if (type === 'reset') {
        phone = this.resetForm.phone
      }
      if (!phone || phone.length !== 11) {
        this.$message.error('请输入正确的手机号')
        return
      }
      this.sendSmsType = type;
      this.sendSmsPhone = phone;
      this.sendSmsShow = true
    },
    sendSmsClose() {
      this.sendSmsShow = false
    },
    sendSmsSuccess() {
      this.sendSmsClose()
      this.sendSmsCountdown = 60
      this.doCountdown()
    },
    doCountdown() {
      if (this.sendSmsCountdown > 0) {
        this.sendSmsCountdown -= 1
        setTimeout(() => {
          this.doCountdown()
        }, 1000)
      }
    },
    doLogin() {
      const phone = this.loginForm.phone
      const password = this.loginForm.password
      if (!phone) {
        this.$message.error('请输入手机号')
        return
      }
      if (!password) {
        this.$message.error('请输入登录密码')
        return
      }
      login({
        phone: phone,
        password: password
      }).then(res => {
        setToken(res.data.token)
        setStorage('phone', phone)
        this.$message.success(res.message)
        setTimeout(() => {
          window.location.reload()
        }, 300)
      })
    },
    doReg() {
      const phone = this.regForm.phone
      const code = this.regForm.code
      const password = this.regForm.password
      if (!phone) {
        this.$message.error('请输入手机号')
        return
      }
      if (phone.length !== 11) {
        this.$message.error('手机号码格式不正确')
        return
      }
      if (!code) {
        this.$message.error('请输入短信验证码')
        return
      }
      if (!password) {
        this.$message.error('请输入登录密码')
        return
      }
      reg({
        phone: phone,
        password: password,
        code: code
      }).then(res => {
        setStorage('phone', phone)
        this.switchFormType('login')
        this.$message.success(res.message)
      })
    },
    doReset() {
      const phone = this.resetForm.phone
      const code = this.resetForm.code
      const password = this.resetForm.password
      if (!phone) {
        this.$message.error('请输入手机号')
        return
      }
      if (phone.length !== 11) {
        this.$message.error('手机号码格式不正确')
        return
      }
      if (!code) {
        this.$message.error('请输入短信验证码')
        return
      }
      if (!password) {
        this.$message.error('请输入登录密码')
        return
      }
      reset({
        phone: phone,
        password: password,
        code: code
      }).then(res => {
        setStorage('phone', phone)
        this.switchFormType('login')
        this.$message.success(res.message)
      })
    },
    toDoc(type) {
      toDoc(type)
    }
  }
}
</script>

<style lang="scss">
.form {
  .el-form-item__content {
    line-height: 40px;
    position: relative;
    font-size: 14px;
  }

  .el-input {
    display: inline-block;
    height: 46px;
    width: 85%;

    .el-input__inner {
      border: none;
      background: none;
      outline: none;
    }

    input {
      background: transparent;
      border: 0;
      -webkit-appearance: none;
      border-radius: 0;
      padding: 12px 5px 12px 15px;
      color: #444;
      height: 46px;
      caret-color: #666;
      font-size: 16px;
    }
  }
}
</style>

<style lang="scss" scoped>
.form {
  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.08);
    border-radius: 5px;
    color: #454545;
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: #889aa4;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .sendcode {
    position: absolute;
    right: 12px;
    top: 12px;
    font-size: 13px;
    color: #889aa4;
    user-select: none;
    background: #fff;
    padding: 6px 10px;
    border-radius: 5px;
    &:hover {
      background: #f8f8f8;
    }
  }

  .show-pwd {
    position: absolute;
    right: 12px;
    top: 7px;
    font-size: 16px;
    color: #889aa4;
    cursor: pointer;
    user-select: none;
  }
}

.container {
  width: 1080px;
  height: 410px;
  position: relative;
  top: -10px;
  transition: left 0.15s ease-in-out;
  .navs {
    text-align: center;
    .btn {
      display: inline-block;
      margin: 0 20px;
    }
  }
  .agree {
    color: #999;
    font-size: 13px;
    text-align: center;
    margin-top: 50px;
    a:hover {
      color: #10a37f;
    }
  }

  .title-container {
    font-size: 20px;
    text-align: center;
    height: 48px;
    line-height: 48px;
    margin-bottom: 20px;
    letter-spacing: 2px;
  }

  .reg-container {
    position: absolute;
    left: 0;
    top: 0;
    width: 360px;
    padding: 0 40px;
  }

  .login-container {
    position: absolute;
    left: 360px;
    top: 0;
    width: 360px;
    padding: 0 40px;
    .login-type {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 20px 0 25px 0;
      .item {
        width: 120px;
        height: 48px;
        line-height: 48px;
        position: relative;
        text-align: center;
        font-size: 16px;
        letter-spacing: 2px;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        span {
          width: 40px;
          height: 4px;
          position: absolute;
          bottom: 0;
          left: 50%;
          margin-left: -20px;
          background: #10a37f;
          display: block;
          opacity: 0;
          border-radius: 2px;
          transition: all 0.2s ease-in-out;
        }
        &.active {
          font-size: 18px;
          span {
            opacity: 1;
          }
        }
      }
    }
    .qrcode {
      width: 240px;
      height: 240px;
      box-sizing: border-box;
      text-align: center;
      background: #f8f8f8;
      margin: 0 auto;
      img {
        width: 240px;
        height: 240px;
        border-radius: 10px;
        margin: 0 auto;
      }
    }
    .tips {
      height: 30px;
      line-height: 30px;
      margin-top: 15px;
      color: #999;
      letter-spacing: 2px;
      font-size: 16px;
    }
  }
  .reset-container {
    position: absolute;
    left: 720px;
    top: 0;
    width: 360px;
    padding: 0 40px;
  }

}
</style>

