<template>
  <div style="margin-bottom: 10px !important;">
    <div v-if="item.state === 0" class="writing">
      <draw-loading :title="'排队中，请稍候...' | lang" color="#04BABE" text-color="#999" />
    </div>
    <div v-else-if="item.state === 3" class="writing">
      <div v-if="item.response && item.response[0]">
        <div class="writing" style="position: absolute; left: 0; top: 0; z-index: 2; background: rgba(0, 0, 0, 0.5)">
          <draw-loading :title="'生成中，预计需要1-3分钟左右' | lang" color="#04BABE" text-color="#ccc" />
        </div>
        <el-image :src="item.response[0]" />
      </div>
      <div v-else>
        <draw-loading :title="'生成中，预计需要1-3分钟左右' | lang" color="#04BABE" text-color="#999" />
      </div>
    </div>
    <div v-else-if="item.state === 1">
      <div style="overflow: hidden; border-radius: 10px; width: 512px;">
        <div v-for="(image, index) in item.response" class="image" :class="{small: item.response.length > 1}">
          <el-image
            :src="image"
            :preview-src-list="item.response"
            :title="'点击查看' | lang"
          />
          <div class="ctrls">
            <span title="下载图片" @click.stop="download(image, index)"><i class="el-icon-download" /></span>
            <span v-if="item.platform === 'mj' && item.response.length === 4" :title="'扩大（U' + (index+1) + '）'" @click.stop="mjCtrl(item.draw_id, 'upscale', index + 1)"><svg-icon icon-class="ic_draw_u" /></span>
            <span v-if="item.platform === 'mj' && item.response.length === 4" :title="'演变（V' + (index+1) + '）'" @click.stop="mjCtrl(item.draw_id, 'variation', index + 1)"><svg-icon icon-class="ic_draw_v" /></span>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="item.state === 2" class="writing fail">
      <div class="errmsg">
        <i class="el-icon-error" />
        <span style="max-width: 460px;">{{ '生成失败' | lang }}: {{ item.errmsg }} </span>
      </div>
      <div><span class="btn-retry" @click="retry(item.draw_id)">{{ '重新生成' | lang }}</span></div>
    </div>
  </div>
</template>

<script>
import drawLoading from './drawLoading'
import fileSaver from 'file-saver'
export default {
  components: { drawLoading },
  props: {
    item: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    item: {
      handler(val) {
        // console.log('val', val)
      },
      deep: true
    }
  },
  methods: {
    previewImage(image) {
      this.$emit('preview', image)
    },
    retry(draw_id) {
      this.$emit('retry', draw_id)
    },
    mjCtrl(draw_id, type, index = 1) {
      this.$emit('mjCtrl', draw_id, type, index)
    },
    download(image, index) {
      let filename = 'image-' + (index + 1)
      if (image.indexOf('.png') !== -1) {
        filename += '.png'
      } else if (image.indexOf('.jpg') !== -1) {
        filename += '.jpg'
      }
      fileSaver.saveAs(image, filename)
    }
  }
}
</script>
<style lang="scss" scoped>
.image {
  width: 100%;
  box-sizing: border-box;
  float: left;
  max-width: 512px;
  transition: all 0.1s;
  position: relative;
  .el-image {
    float: left;
  }
  .ctrls {
    position: absolute;
    right: 0;
    bottom: 0;
    display: none;
    flex-direction: column;
    border-top-left-radius: 10px;
    overflow: hidden;
    span {
      display: block;
      padding: 5px 10px;
      background: rgba(96, 98, 102, 0.8);
      color: #fff;
      text-align: center;
      cursor: pointer;
      &:hover {
        background: rgba(0,0,0,0.6)
      }
    }
  }
  &:hover {
    opacity: 0.9;
    .ctrls {
      display: flex;
    }
  }
  &.small {
    width: 256px;
  }
}

.mj-ctrl {
  float: left;
  width: 140px;
  margin-left: 20px;
  .title {
    line-height: 40px;
    color: #666;
    font-size: 14px;
  }
  span {
    display: inline-block !important;
    width: 48px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #e6e6e6;
    border-radius: 4px;
    margin-right: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    color: #666;
    transition: all 0.1s;
    &:hover {
      opacity: 0.75;
    }
  }
}

.writing {
  width: 512px;
  height: 512px;
  background: #eff0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.fail {
  flex-direction: column;
}

.fail .errmsg {
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  line-height: 22px;
  padding: 15px 0;
  overflow: hidden;
  max-height: 105px;
}
.fail .errmsg i {
  font-size: 18px;
  margin-right: 5px;
  position: relative;
  top: 3px;
  color: #dd0000;
}
.fail .btn-retry {
  color: #666;
  padding: 5px 12px;
  background: #fff;
  border-radius: 5px;
  margin-top: 10px;
  font-size: 12px;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}
</style>
