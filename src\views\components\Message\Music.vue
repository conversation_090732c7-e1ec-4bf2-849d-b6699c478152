<template>
  <div>
    <div v-if="music.state === 0">
      <div v-if="result && result.video_url" class="video">
        <img class="thumb" :src="result.image_url" />
        <div class="btn-play"><svg-icon icon-class="ic_music_play"></svg-icon></div>
      </div>
      <div v-else class="writing">
        <music-loading :title="'生成中，预计需要1-3分钟左右' | lang" color="#04BABE" text-color="#999" />
      </div>
    </div>
    <div v-else-if="music.state === 1">
      <div v-if="result && result.video_url" class="video">
        <img class="thumb" :src="result.image_url" />
        <div class="btn-play"><svg-icon icon-class="ic_music_play"></svg-icon></div>
      </div>
      <div v-else class="writing fail">
        <div class="errmsg">
          <i class="el-icon-error" />
          <span style="max-width: 460px;">{{ '生成失败' | lang }}</span>
        </div>
      </div>
    </div>
    <div v-else-if="music.state === 2" class="writing fail">
      <div class="errmsg">
        <i class="el-icon-error" />
        <span style="max-width: 460px;">{{ music.errmsg || '生成失败' }} </span>
      </div>
      <div><span class="btn-retry" @click="retry(music.music_id)">{{ '重新生成' | lang }}</span></div>
    </div>
  </div>
</template>

<script>
import musicLoading from './musicLoading'
import fileSaver from 'file-saver'
export default {
  components: { musicLoading },
  props: {
    music: {
      type: Object,
      default() {
        return {}
      }
    },
    result: {
      type: Object / String,
      default() {
        return {}
      }
    }
  },
  methods: {
    previewImage(image) {
      this.$emit('preview', image)
    },
    retry(music_id) {
      this.$emit('retry', music_id)
    },
    download(video) {
      fileSaver.saveAs(video, 'video.mp4')
    }
  }
}
</script>
<style lang="scss" scoped>
.video {
  width: 256px;
  height: 256px;
  box-sizing: border-box;
  transition: all 0.1s;
  position: relative;
  background: #1f1f1f;
  overflow: hidden;
  border-radius: 10px;
  text-align: center;
  .thumb {
    width: 256px;
    height: 256px;
  }
  .btn-play {
    display: block;
    font-size: 52px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 32px;
    opacity: 0.8;
    color: #000;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -26px;
    margin-top: -26px;
    cursor: pointer;
    transition: all 0.2s linear;
  }
  &:hover .btn-play {
    opacity: 1;
  }

  .btn-download {
    display: block;
    padding: 3px 10px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    text-align: center;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 20px;
    border-radius: 10px;
    z-index: 2;
    &:hover {
      background: rgba(0, 0, 0, 1)
    }
  }
  &:hover {
    opacity: 0.9;
    .ctrls {
      display: flex;
    }
  }
  &.small {
    width: 256px;
  }
}

.writing {
  width: 256px;
  height: 256px;
  background: #eff0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.fail {
  flex-direction: column;
}

.fail .errmsg {
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  line-height: 22px;
  padding: 15px 0;
  overflow: hidden;
  max-width: 220px;
  max-height: 120px;
}
.fail .errmsg i {
  font-size: 18px;
  margin-right: 5px;
  position: relative;
  top: 3px;
  color: #dd0000;
}
.fail .btn-retry {
  color: #666;
  padding: 5px 12px;
  background: #fff;
  border-radius: 5px;
  margin-top: 10px;
  font-size: 12px;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}
</style>
