<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      title="播放音乐"
      :visible="true"
      width="520px"
      :show-close="true"
      :append-to-body="true"
      :before-close="closeMusic"
    >
      <div class="container">
        <div class="video">
          <video
            :src="music.result.video_url"
            class="uni-video-video"
            loop
            autoplay
            playsinline
            controls
            disablepictureinpicture
            controlslist="nodownload noplaybackrate"
            play-btn-position="center"
            style="object-fit: contain;"
          />
        </div>
        <div class="tools">
          <div class="btn-group">
            <el-button size="small" type="primary" icon="el-icon-document-copy" title="复用参数，生成同款音乐" @click="copyOptions">复制参数</el-button>
            <el-button v-if="music.result.video_url" size="small" type="default" icon="el-icon-download" @click="download('video')">下载mp4视频</el-button>
            <el-button v-if="music.result.audio_url" size="small" type="default" icon="el-icon-download" @click="download('audio')">下载mp3音频</el-button>
            <el-button v-if="music.result.prompt" size="small" type="default" icon="el-icon-document-copy" @click="copyText(music.result.prompt)">复制歌词</el-button>
          </div>
          <span v-if="music.seed" title="种子值 --seed" style="font-size: 12px; color: #898989; float: right;"><svg-icon class="icon" icon-class="ic_seed" /> {{ music.seed }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import fileSaver from 'file-saver'
export default {
  props: {
    music: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  methods: {
    closeMusic() {
      this.$emit('close')
    },
    download(type) {
      var url = ''
      if (type === 'video') {
        url = this.music.result.video_url
      } else if (type === 'audio') {
        url = this.music.result.audio_url
      }
      var filename = url.split('/').pop()
      fileSaver.saveAs(url, filename)
    },
    copyOptions() {
      this.$emit('copyOptions', this.music.music_id)
      this.closeMusic()
    },
    copyText(text) {
      const _this = this
      this.$copyText(text).then(
        function() {
          _this.$message.success(_this.$lang('已复制'))
        },
        function() {
          _this.$message.error(_this.$lang('复制失败，请重试'))
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.video {
  width: 480px;
  height: 720px;
  text-align: center;
  background: #1f1f1f;
  border-radius: 5px;
  margin-top: -15px;
  overflow: hidden;
  video {
    height: 100%;
    max-width: 100%;
  }
}

.tools {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 25px 0 5px 0;
  .btn-group {
    display: flex;
    align-items: center;
  }
}

.param {
  padding: 15px 0;
  .prompt {
    font-size: 14px;
    line-height: 24px;
    color: #666;
    padding: 10px 15px;
    background: #f7f7f8;
    border-radius: 5px;

    .icon {
      margin-right: 5px;
      display: inline;
    }
  }
}

</style>
