<template>
  <div class="notice">
    <div class="alert" v-if="type === 'alert'">
      <el-alert type="success" icon="" :title="content"></el-alert>
    </div>
    <el-dialog
      v-if="type === 'dialog'"
      custom-class="my-dialog notice-dialog"
      width="600px"
      :title="'公告' | lang"
      :visible="true"
      :close-on-click-modal="true"
      :append-to-body="true"
      :before-close="doClose"
    >
      <div style="height: 450px; overflow: hidden;">
        <el-scrollbar wrap-class="scrollbar-wrapper">
          <div style="padding: 0 30px 20px 30px;">
            <div v-html="content" class="content"></div>
          </div>
        </el-scrollbar>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" plain icon="el-icon-close" size="small" @click="doClose">{{ '我知道了' | lang }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  methods: {
    doClose() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.notice {
  .alert {
    position: fixed;
    top: 15px;
    left: 50%;
    margin-left: -600px;
    width: 1200px;
    z-index: 999;
  }
}
</style>
