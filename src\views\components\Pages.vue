<template>
  <div class="pages" v-if="pageTotal > 1">
    <span @click="toPrev" style="width: 88px;">上页</span>
    <div style="text-align: center;">
      <span :class="{active: page === item}" v-for="item in pageTotal" @click="toPage(item)">{{item}}</span>
    </div>
    <span @click="toNext" style="width: 88px;">下页</span>
  </div>
</template>

<script>
export default {
  props: {
    page: {
      type: Number,
      default: 1
    },
    pageTotal: {
      type: Number,
      default: 1
    }
  },
  methods: {
    toPage(page) {
      if (this.page !== page) {
        this.$emit('change', page)
      }
    },
    toPrev() {
      if (this.page > 1) {
        this.toPage(this.page - 1)
      }
    },
    toNext() {
      if (this.page < this.pageTotal) {
        this.toPage(this.page + 1)
      }
    }
  }
};
</script>
