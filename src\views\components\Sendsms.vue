<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      title=""
      :visible="true"
      width="320px"
      :close-on-click-modal="false"
      :show-close="false"
      :append-to-body="true"
    >
      <el-form ref="form" label-width="70px">
        <el-form-item :label="'验证码' | lang" prop="title">
          <div style="display: flex; align-items: center;">
            <el-input v-model="code" ref="input" placeholder="" size="small" style="width: 90px; font-size: 18px; font-weight: bold; letter-spacing: 2px;" />
            <div class="captcha" @click="getCaptcha" :title="'点击更换验证码' | lang">
              <img :src="captcha" />
            </div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" icon="el-icon-close" size="small" @click="doClose">{{ '取 消' | lang }}</el-button>
        <el-button type="primary" icon="el-icon-check" size="small" @click="doSubmit">{{ '确 定' | lang }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCaptcha, sendSms } from '@/api/login'

export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    phone: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      captcha: '',
      token: '',
      code: ''
    }
  },
  created() {
    this.getCaptcha()
  },
  methods: {
    doClose() {
      this.$emit('close')
    },
    doSubmit() {
      if (!this.code) {
        this.$message.error('请输入验证码')
        return false
      }
      sendSms({
        type: this.type,
        phone: this.phone,
        code: this.code,
        token: this.token
      }).then(res => {
        this.$message.success(res.message)
        this.$emit('success')
      })
      return false
    },
    getCaptcha() {
      getCaptcha().then(res => {
        this.captcha = res.data.image
        this.token = res.data.token
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .captcha {
    width: 104px;
    height: 32px;
    background: #f8f8f8;
    display: inline-block;
    margin-left: 5px;
    cursor: pointer;
    img {
      height: 32px;
    }
  }
</style>

