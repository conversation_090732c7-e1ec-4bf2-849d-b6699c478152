<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      title=""
      :visible="true"
      width="380px"
      :before-close="closeForm"
    >
      <div class="container">
        <div class="bind-container">
          <div class="title-container">{{ '绑定手机号' | lang }}</div>
          <el-form ref="form" :model="form" class="form" auto-complete="on" label-position="left">
            <el-form-item prop="phone">
              <span class="svg-container">
                <svg-icon icon-class="ic_login_phone" style="font-size: 20px; position: relative; left: -2px;" />
              </span>
              <el-input
                ref="username"
                v-model="form.phone"
                :placeholder="'手机号' | lang"
                name="phone"
                type="text"
                auto-complete="on"
              />
            </el-form-item>

            <el-form-item prop="code" >
              <span class="svg-container">
                <svg-icon icon-class="ic_login_code" style="font-size: 20px; position: relative; left: -2px;" />
              </span>
              <el-input
                ref="code"
                v-model="form.code"
                type="text"
                :placeholder="'短信验证码' | lang"
                maxlength="6"
                name="password"
              />
              <el-button type="text" class="sendcode" size="small" @click="doSendSms('bind')" :disabled="sendSmsCountdown > 0">
                {{ sendSmsCountdown > 0 ? `${sendSmsCountdown}s ` + $lang('后可重发') : $lang('发送验证码') }}
              </el-button>
            </el-form-item>
            <el-form-item prop="password">
              <span class="svg-container">
                <svg-icon icon-class="ic_login_pwd" style="font-size: 16px;" />
              </span>
              <el-input
                :key="passwordType"
                ref="password"
                v-model="form.password"
                :type="passwordType"
                :placeholder="'设置登录密码' | lang"
              />
              <span class="show-pwd" @click="showPwd">
              <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span>
            </el-form-item>

            <el-button type="primary" style="width:100%; margin-top: 20px; letter-spacing: 1px;" @click.native.prevent="doSubmit">{{ '确定修改' | lang }}</el-button>
          </el-form>
        </div>
      </div>
      <Sendsms v-if="sendSmsShow" :phone="sendSmsPhone" :type="sendSmsType" @success="sendSmsSuccess" @close="sendSmsClose" />

    </el-dialog>
  </div>
</template>

<script>
import { bindPhone } from '@/api/user';
import Sendsms from '../Sendsms'

export default {
  components: { Sendsms },
  props: {
    phone: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      sendSmsType: 'bind',
      sendSmsPhone: '',
      sendSmsShow: false,
      sendSmsCountdown: 0,
      form: null,
      passwordType: 'password'
    }
  },
  created() {
    this.form = {
      phone: this.phone,
      code: '',
      password: ''
    }
  },
  methods: {
    closeForm() {
      this.$emit('close')
    },
    doSendSms(type) {
      const phone = this.form.phone
      if (!phone || phone.length !== 11) {
        this.$message.error('请输入正确的手机号')
        return
      }
      this.sendSmsType = type;
      this.sendSmsPhone = phone;
      this.sendSmsShow = true
    },
    sendSmsClose() {
      this.sendSmsShow = false
    },
    sendSmsSuccess() {
      this.sendSmsClose()
      this.sendSmsCountdown = 60
      this.doCountdown()
    },
    doCountdown() {
      if (this.sendSmsCountdown > 0) {
        this.sendSmsCountdown -= 1
        setTimeout(() => {
          this.doCountdown()
        }, 1000)
      }
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
    },
    doSubmit() {
      const phone = this.form.phone
      const code = this.form.code
      const password = this.form.password
      if (!phone) {
        this.$message.error('请输入手机号')
        return
      }
      if (phone.length !== 11) {
        this.$message.error('手机号码格式不正确')
        return
      }
      if (!password) {
        this.$message.error('请设置登录密码')
        return
      }
      if (!code) {
        this.$message.error('请输入短信验证码')
        return
      }
      bindPhone({
        phone: phone,
        code: code,
        password: password
      }).then(res => {
        this.$message.success(res.message)
        this.$emit('success')
        this.closeForm()
      })
    }
  }
}
</script>

<style lang="scss">
.form {
  .el-form-item__content {
    line-height: 40px;
    position: relative;
    font-size: 14px;
  }

  .el-input {
    display: inline-block;
    height: 46px;
    width: 85%;

    .el-input__inner {
      border: none;
      background: none;
      outline: none;
    }

    input {
      background: transparent;
      border: 0;
      -webkit-appearance: none;
      border-radius: 0;
      padding: 12px 5px 12px 15px;
      color: #444;
      height: 46px;
      caret-color: #666;
      font-size: 16px;
    }
  }
}
</style>

<style lang="scss" scoped>
.form {
  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.08);
    border-radius: 5px;
    color: #454545;
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: #889aa4;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .sendcode {
    position: absolute;
    right: 12px;
    top: 12px;
    font-size: 13px;
    color: #889aa4;
    user-select: none;
    background: #fff;
    padding: 6px 10px;
    border-radius: 5px;
    &:hover {
      background: #f8f8f8;
    }
  }

  .show-pwd {
    position: absolute;
    right: 12px;
    top: 7px;
    font-size: 16px;
    color: #889aa4;
    cursor: pointer;
    user-select: none;
  }
}

.container {
  padding: 0 20px;
  height: 360px;
  transition: left 0.15s ease-in-out;
  .navs {
    text-align: center;
    .btn {
      display: inline-block;
      margin: 0 20px;
    }
  }

  .title-container {
    font-size: 20px;
    text-align: center;
    height: 32px;
    line-height: 32px;
    margin-bottom: 25px;
    letter-spacing: 2px;
  }

}
</style>
