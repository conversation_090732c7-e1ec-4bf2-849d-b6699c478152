<template>
  <div>
    <el-dialog
      custom-class="my-dialog"
      :title="'个人中心' | lang"
      :visible="true"
      width="500px"
      :close-on-click-modal="true"
      :before-close="closeUserInfo"
    >
      <div class="user-container">
        <div class="box-user">
          <div class="userinfo" @click="showSetting">
            <div class="avatar">
              <img :src="userinfo.avatar || '/static/img/no_avatar.png'">
            </div>
            <div class="info">
              <div class="nickname">
                <span>{{ userinfo.nickname || $lang('无昵称') }}</span>
              </div>
              <div style="display: flex; align-items: center; margin-top: 5px;">
                <div class="mid">ID: {{ userinfo.user_id }}</div>
                <div class="phone">
                  <svg-icon class="icon" icon-class="ic_login_phone"></svg-icon>
                  <span>{{ userinfo.phone || '未绑定手机号' }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="wallet">
            <div class="item" @click="showPay('point')">
              <div>
                <span class="num">{{ userinfo.balance_point }}</span>
              </div>
              <div class="label">{{ priceSetting.title }}{{ '余额' | lang }}</div>
            </div>
          </div>
        </div>
        <div class="box-vip">
          <div style="display: flex; align-items: center; line-height: 24px;">
            <img class="icon" src="@/assets/ic_vip.png">
            <span>{{ 'VIP会员' | lang }}</span>
            <div class="line" />
            <span v-if="userinfo.vip_expire_time">{{ userinfo.vip_expire_time }} {{ '到期' | lang }}</span>
            <span v-else>{{ '高速通道 无限对话' | lang }}</span>
          </div>
          <div @click="showPay('vip')">
            <button v-if="userinfo.vip_expire_time" class="btn-vip" style="width: 46px;">{{ '续费' | lang }}</button>
            <button v-else class="btn-vip">{{ '立即开通' | lang }}</button>
          </div>
        </div>

        <div class="logout">
          <el-button type="danger" size="mini" @click="logout">{{ '退出登录' | lang }}</el-button>
          <div class="links">
            <a @click="toDoc('service')">{{ '《服务协议》' | lang }}</a>
            <a @click="toDoc('privacy')">{{ '《隐私政策》' | lang }}</a>
            <a @click="toDoc('legal')">{{ '《免责声明》' | lang }}</a>
          </div>
        </div>
      </div>
    </el-dialog>
    <setting v-if="settingShow" :avatar="userinfo.avatar" :nickname="userinfo.nickname" @close="closeSetting" @success="freshUserInfo" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getInfo } from '@/api/user'
import { toDoc } from '@/utils/util'
import setting from './setting'
export default {
  name: 'Userinfo',
  components: { setting },
  data() {
    return {
      userinfo: {},
      settingShow: false
    }
  },
  computed: {
    ...mapGetters([
      'hasModel4',
      'model4Name',
      'drawIsOpen',
      'videoIsOpen',
      'musicIsOpen',
      'priceSetting'
    ])
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      getInfo().then(res => {
        this.userinfo = res.data
      })
    },
    freshUserInfo() {
      this.$store.dispatch('user/getInfo')
      this.getUserInfo()
    },
    closeUserInfo() {
      this.$emit('close')
    },
    showPay(type) {
      this.$emit('showPay', type)
    },
    showSetting() {
      this.settingShow = true
    },
    closeSetting() {
      this.settingShow = false
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.closeUserInfo()
      window.location.reload()
    },
    toDoc(type) {
      toDoc(type)
    }
  }
}
</script>

<style lang="scss" scoped>
  .user-container {
    overflow: hidden;
    padding: 0 25px;
    position: relative;
    .box-user {
      position: relative;
      width: 100%;
      left: 0;
      top: 0;
      height: 100px;
      box-sizing: content-box;
      z-index: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .userinfo {
        display: flex;
        align-items: center;
        width: 240px;
        height: 80px;
        //padding: 0 20px;
        cursor: pointer;
        position: relative;

        .avatar {
          width: 50px;
          height: 50px;
          min-width: 50px;
          border-radius: 5px;
          overflow: hidden;
          background-color: #f7f7f7;
          img {
            width: 100%;
            height: 100%;
          }
        }

        .info {
          display: flex;
          flex-direction: column;
          margin-left: 15px;
          .nickname {
            font-size: 15px;
            font-weight: bold;
            margin-top: 4px;
            display: flex;
            align-items: center;
            .icon {
              cursor: pointer;
            }

            .btn-setting {
              margin-left: 15px;
              font-size: 15px;
              cursor: pointer;
              border-radius: 5px;
              color: #666;
              display: none;
            }
          }
          .mid {
            font-size: 14px;
            line-height: 26px;
          }
          .phone {
            color: #10a37f;
            background-color: #e3f5f0;
            padding: 2px 5px;
            height: 18px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            margin-left: 10px;
            .icon {
              font-size: 14px;
            }
            span {
              font-size: 12px;
            }
          }
        }
        &:hover {
          .btn-setting {
            display: block !important;
          }
        }
      }

      .wallet {
        display: flex;
        justify-content: space-between;
        align-items: center;
        overflow: hidden;
        margin: 15px 0;
        .item {
          text-align: center;
          line-height: 24px;
          padding: 10px 0;
          border-radius: 10px;
          width: 120px;
          height: 80px;
          box-sizing: border-box;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .num {
            font-size: 18px;
            color: #10a37f;
            font-weight: bold;
            letter-spacing: 1px;
          }
          .label {
            color: #666;
            font-size: 12px;
            letter-spacing: 1px;
          }
        }
        &:hover {
          background: #f7f7f7;
        }
      }
    }
  }

  .box-vip {
    width: 400px;
    height: 44px;
    border: none;
    position: relative;
    background: linear-gradient(30deg, #f7debe, #f4c384);
    border-radius: 10px;
    font-size: 13px;
    color: #9a5b12;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 15px;
    margin-top: 10px;

    .icon {
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    .line {
      margin: 0 10px;
      vertical-align: middle;
      display: inline-block;
      width: 1px;
      height: 12px;
      background: rgba(154, 91, 18, 0.4);
    }
    .btn-vip {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 72px;
      height: 24px;
      background: #ffe8b5;
      border-radius: 12px;
      font-size: 12px;
      padding: 0;
      border: none;
      color: #9a5b12;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
  }

  .logout {
    text-align: center;
    margin-top: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .links {
      width: 240px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      a {
        color: #999;
        font-size: 13px;
        &:hover {
          color: #10a37f;
        }
      }
    }
  }

</style>

