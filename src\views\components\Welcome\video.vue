<template>
  <div class="welcome">
    <div class="nodata">
      <img src="/static/img/draw_nodata.png">
      <p class="tips">{{ desc }}</p>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    desc: {
      type: String,
      default: ''
    }
  }
}

</script>

<style lang="scss" scoped>
.welcome {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  text-align: center;
  position: absolute;
  left: 50%;
  margin-left: -100px;
  top: 50%;
  margin-top: -100px;
  .nodata {
    margin: 0 auto;
    .tips {
      color: #999;
      font-size: 16px;
      margin-top: 20px;
    }
  }
}
</style>
