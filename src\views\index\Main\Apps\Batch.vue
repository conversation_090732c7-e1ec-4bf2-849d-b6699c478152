<template>
  <div class="main-batch">
    <el-scrollbar v-if="!activeBatch" wrap-class="scrollbar-wrapper">
      <div class="batch-list">
        <div v-if="page === 1" class="batch-item btn-add" @click="showBatchForm('')">
          <i class="el-icon-plus" style="font-size: 48px;"></i>
          <div style="margin-top: 15px;">创建任务组</div>
        </div>
        <div v-for="item in batchList" class="batch-item" @click="openBatchConsole(item.batch_id)">
          <div class="header">
            <span class="count">任务 {{ item.count_finished }}/{{ item.count_total }}</span>
            <el-tag type="info" size="small" v-if="item.count_finished === 0">未开始</el-tag>
            <el-tag type="warning" size="small" v-else-if="item.count_finished < item.count_total">未完成</el-tag>
            <el-tag type="success" size="small" v-else-if="item.count_finished === item.count_total">已完成</el-tag>
          </div>
          <div class="body">{{ item.prompt || '没有定义指令' }}</div>
          <div class="footer">
            <div class="time">
              <span>{{ item.create_time }}</span>
            </div>
            <div class="ops">
              <i class="el-icon-edit" title="编辑" @click.stop="showBatchForm(item.batch_id)"></i>
              <i class="el-icon-delete text-danger" title="删除" @click.stop="delBatch(item.batch_id)"></i>
            </div>
          </div>
        </div>
        <div style="clear: both;"></div>
      </div>
      <pages :page="page" :page-total="pageTotal" @change="changePage"></pages>
    </el-scrollbar>
    <batch-task v-else :batch_id="activeBatch.batch_id" :ai="activeBatch.ai" @close="closeBatchConsole" @showBatchForm="showBatchForm" @showLogin="showLogin" @showPay="showPay"></batch-task>
    <div v-if="batchForm">
      <el-dialog
        custom-class="my-dialog"
        :title="batchForm.batch_id ? '任务设置' : '创建新任务' | lang"
        width="660px"
        :visible="true"
        :close-on-click-modal="true"
        :append-to-body="true"
        :before-close="closeBatchForm"
      >
        <el-form ref="form" :model="batchForm" label-width="100px" style="padding: 20px 0;">
          <el-form-item :label="'AI通道' | lang">
            <el-select v-model="batchForm.ai" placeholder="请选择">
              <el-option
                v-for="item in aiList"
                :label="item.alias"
                :value="item.name"
              />
            </el-select>
            <div v-if="isGpt4(batchForm.ai)" class="text-primary">
              <p v-if="priceSetting.text40 > 0" style="line-height: 24px; margin: 5px 0; font-size: 16px;">高级通道，每个子任务消耗 {{ priceSetting.text40 }} {{ priceSetting.title }}<span v-if="priceSetting.text40_vip">（会员免费）</span></p>
              <p v-else style="line-height: 24px; margin: 5px 0; font-size: 16px;">高级通道，免费使用</p>
            </div>
            <div v-else class="text-primary">
              <p v-if="priceSetting.text35 > 0" style="line-height: 24px; margin: 5px 0; font-size: 16px;">每个子任务消耗 {{ priceSetting.text35 }} {{ priceSetting.title }}<span v-if="priceSetting.text35_vip">（会员免费）</span></p>
              <p v-else style="line-height: 24px; margin: 5px 0; font-size: 16px;">免费使用</p>
            </div>
          </el-form-item>
          <el-form-item :label="'前置指令' | lang">
            <el-input type="textarea" :rows="8" v-model="batchForm.prompt" :placeholder="'如：请按照下面标题，写一篇3000字文章，符合SEO要求，并在文章结尾生成5个关键词' | lang" size="normal" style="width: 480px;" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="default" icon="el-icon-close" size="small" @click="closeBatchForm">{{ '取 消' | lang }}</el-button>
          <el-button type="primary" icon="el-icon-check" size="small" @click="saveBatchInfo">{{ '确 定' | lang }}</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getBatchList, getBatchInfo, saveBatchInfo, delBatch } from '@/api/batch'
import batchTask from '@/views/index/components/batchTask'
import pages from '@/views/components/Pages'
export default {
  name: 'Batch',
  components: { batchTask, pages },
  data() {
    return {
      activeBatch: null,
      batchList: [],
      batchForm: null,
      page: 1,
      pagesize: 39,
      batchTotal: 0
    }
  },
  computed: {
    ...mapGetters([
      'aiList',
      'priceSetting'
    ]),
    pageTotal() {
      return Math.ceil(this.batchTotal / this.pagesize)
    }
  },
  watch: {
    '$route.query.batch_id'() {
      if (!this.$route.query.batch_id) {
        this.activeBatch = null
      } else {
        this.getBatchInfo(this.$route.query.batch_id)
      }
    }
  },
  mounted() {
    this.getBatchList()
    if (this.$route.query.batch_id) {
      this.getBatchInfo(this.$route.query.batch_id)
    }
  },
  methods: {
    getBatchList() {
      getBatchList({
        page: this.page,
        pagesize: this.pagesize
      }).then(res => {
        this.batchList = res.data.list
        this.batchTotal = res.data.count
      }).catch(err => {
        if (err.errno === 403) {
          this.showLogin()
        }
      })
    },
    changePage(page) {
      if (this.page !== page) {
        this.page = page
        this.getBatchList()
      }
    },
    showBatchForm(batch_id = '') {
      if (batch_id) {
        getBatchInfo({
          batch_id: batch_id
        }).then(res => {
          this.batchForm = {
            batch_id: batch_id,
            ai: res.data.ai,
            prompt: res.data.prompt
          }
        }).catch(err => {
          if (err.errno === 403) {
            this.showLogin()
          }
        })
      } else {
        this.batchForm = {
          ai: this.aiList.length > 0 ? this.aiList[0].name : '',
          prompt: ''
        }
      }
    },
    getBatchInfo(batch_id) {
      getBatchInfo({
        batch_id: batch_id
      }).then(res => {
        this.activeBatch = res.data
      }).catch(err => {
        if (err.errno === 403) {
          this.showLogin()
        }
      })
    },
    closeBatchForm() {
      this.batchForm = null
    },
    delBatch(batch_id) {
      this.$confirm('删除后不可恢复，确认删除吗?', '提示', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delBatch({
          batch_id: batch_id
        }).then(res => {
          this.getBatchList()
        }).catch(err => {
          if (err.errno === 403) {
            this.showLogin()
          }
        })
      })
    },
    saveBatchInfo() {
      if (!this.batchForm.ai) {
        this.$message.error('请选择AI通道')
        return
      }
      saveBatchInfo(this.batchForm).then(res => {
        this.$message.success(res.message)
        if (this.activeBatch) {
          this.$set(this.activeBatch, 'ai', this.batchForm.ai)
        }
        this.getBatchList()
        this.closeBatchForm()
      }).catch(err => {
        if (err.errno === 403) {
          this.showLogin()
        }
      })
    },
    openBatchConsole(batch_id) {
      this.$router.push({
        query: { batch_id: batch_id }
      }).catch(err => {})
    },
    closeBatchConsole(batch_id) {
      this.$router.push({
        name: 'batch'
      }).catch(err => {})
    },
    showLogin() {
      this.$emit('showLogin')
    },
    showPay(type) {
      this.$emit('showPay', type)
    },
    isGpt4(name) {
      return ['openai4', 'diy42', 'diy43', 'azure_openai4', 'wenxin4', 'hunyuan4', 'zhipu4', 'claude2'].includes(name)
    },
  }
}
</script>

<style lang="scss">
.scrollbar-wrapper {
  height: 100%;
  overflow-x: hidden;
}
</style>
