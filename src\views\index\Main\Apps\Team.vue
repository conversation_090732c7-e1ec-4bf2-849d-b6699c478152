<template>
  <div class="main-team">
    team

  </div>
</template>

<script>

export default {
  name: 'team',
  components: { },
  data() {
    return {

    }
  },
  computed: {

  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style lang="scss">
.scrollbar-wrapper {
  height: 100%;
  overflow-x: hidden;
}
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
.input .el-textarea__inner {
  padding: 10px 72px 10px 15px;
  letter-spacing: 1px;
  transition: all 0.1s ease-in-out;
  font-size: 16px;
}
</style>

<style lang="scss">
.markdown-body {
  display: block;
  width: 100%;
  background-color: transparent;
  font-size: 14px;
  box-sizing: border-box;

  p {
    white-space: pre-wrap;
    text-align: left;
    &:last-child {
      margin-bottom: 0;
    }
  }

  img[alt=cursor] {
    width: 5px;
    height: 20px;
    position: relative;
    top: 4px;
    left: 2px;
  }

  ol {
    list-style-type: decimal;
  }

  ul {
    list-style-type: disc;
  }
  /**代码行号**/
  .code-linenum {
    text-align: right;
    float: left;
    padding: 0;
    margin: 0 1em 0 0;
  }
  .code-linenum-line {
    list-style: none;
    margin: 0;
    color: #ccc;
    transition: color 0.1s ease-in-out;
  }

  pre code,
  pre tt {
    line-height: 1.6;
    font-size: 16px;
  }
  pre code img[alt=cursor] {
    display: none;
  }

  .highlight pre,
  pre {
    //background-color: #fff;
    background-color: #edeff2;
    margin-top: 16px;
    transition: background 0.1s ease-in-out;
    text-align: left;
  }

  code.hljs {
    padding: 0;
  }

  .code-block {
    &-wrapper {
      position: relative;
      padding-top: 24px;
    }

    &-header {
      position: absolute;
      top: 5px;
      right: 0;
      width: 100%;
      padding: 0 1rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #b3b3b3;

      &__copy {
        cursor: pointer;
        margin-left: 0.5rem;
        user-select: none;

        &:hover {
          color: #65a665;
        }
      }
    }
  }

  .code-block-wrapper {
    display: block;
    width: 100%
  }

}
</style>
