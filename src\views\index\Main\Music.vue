<template>
  <div class="size-small">
    <div class="platform-list">
      <div v-if="sunoSetting && sunoSetting['is_open']" class="item" :class="{active: activePlatform === 'suno'}" @click="switchPlatform('suno')">SunoAI</div>
    </div>
    <div class="sub-sidebar" style="top: 55px;">
      <div class="music-setting">
        <div class="module-body">
          <musicSuno
            v-if="activePlatform === 'suno'"
            ref="musicSetting"
            @showLogin="showLogin"
            @showPay="showPay"
          />
        </div>
        <div v-if="activePlatform === 'suno'" class="module-footer">
          <div v-if="priceSetting.music > 0" class="box-price" @click="showPay">
            <svg-icon class="icon" icon-class="ic_mod_music" />
            <span class="num">{{ priceSetting.music }}</span>{{ priceSetting.title }}/{{ '次' | lang }}
            <span v-if="priceSetting.music_vip" class="vip">会员免费</span>
          </div>
          <div v-else class="box-price" style="cursor: default;">
            <svg-icon class="icon" icon-class="ic_mod_music" />
            <span class="vip">免费使用</span>
          </div>
          <el-button
            class="btn-music"
            type="primary"
            :title="'发送' | lang"
            @click="submitTask"
          >
            <svg-icon icon-class="ic_mod_music" /> 生成音乐
          </el-button>
        </div>
      </div>
    </div>

    <div class="main-wrapper">
      <div v-if="musicShopIsOpen" class="type-list">
        <div class="tab-type">
          <div class="tab-item" :class="{active: listType === 'public'}" @click="switchListType('public')">音乐广场</div>
          <div class="tab-item" :class="{active: listType === 'my'}" @click="switchListType('my')">我的作品</div>
        </div>
      </div>
      <div v-if="musicShopIsOpen && listType === 'public'" class="box-msg-list" style="bottom: 0;">
        <el-scrollbar ref="publicList" wrap-class="scrollbar-wrapper">
          <div class="music-list">
            <div v-for="item in publicList" class="music-item" @mouseenter="videoMouseEnter(item.video_id)" @mouseleave="videoMouseLeave(item.video_id)" @click.stop="showVideoDetail(item)">
              <div class="video" :class="{active: hoverVideoId === item.video_id}">
                <video :id="'public_video_' + item.video_id" muted :poster="item.poster" :src="item.video" loop="loop" preload="none" />
                <div class="poster" :style="'background-image: url(' + item.poster + ');'" />
                <div class="btn-play"><div class="icon" style="background-image: url(/static/img/ic_play.png);" /></div>
                <div class="prompt">{{ item.prompt }}</div>
              </div>
            </div>
          </div>
          <div style="width: 100%; margin: 20px 0;">
            <pages :page="publicPage" :page-total="publicPageTotal" @change="changePublicPage" />
          </div>
        </el-scrollbar>
      </div>
      <div v-else-if="listType === 'my' && myList && myList.length > 0" class="box-msg-list style-write" style="bottom: 0;">
        <el-scrollbar ref="myList" wrap-class="scrollbar-wrapper">
          <div class="list" style="width: 862px;" :style="musicShopIsOpen ? 'padding-top: 70px;' : 'padding-top: 0;'">
            <div v-for="item in myList" class="row">
              <div class="message row-user" style="padding: 20px 30px 10px 30px; width: 862px;">
                <div class="avatar" style="background: #9aa37e;">
                  <img :src="avatar || '/static/img/ic_user.png'">
                </div>
                <div class="text">
                  <p>
                    <span class="label">【模式】</span>
                    <span v-if="item.options.custom_mode === 1">专业模式</span>
                    <span v-else>灵感模式</span>
                  </p>
                  <p>
                    <span class="label">【类型】</span>
                    <span v-if="item.options.make_instrumental === 1">纯音乐</span>
                    <span v-else>歌曲</span>
                  </p>
                  <p v-if="item.options.prompt">
                    <span v-if="item.options.custom_mode === 1" class="label">【歌词】</span>
                    <span v-else class="label">【描述】</span>
                    <span v-if="item.options.prompt.length > 40">{{ item.options.prompt.substring(0, 40) }}...</span>
                    <span v-else>{{ item.options.prompt }}</span>
                  </p>
                  <p v-if="item.options.title">
                    <span class="label">【歌名】</span>
                    <span>{{ item.options.title }}</span>
                  </p>
                  <p v-if="item.options.tags">
                    <span class="label">【曲风】</span>
                    <span>{{ item.options.tags }}</span>
                  </p>
                  <div class="tools" style="opacity: 1;">
                    <span
                      class="btn text-primary"
                      title="将参数填充到左侧面板"
                      @click="copyOptions(item.music_id)"
                    ><svg-icon class="icon" icon-class="ic_copy" />{{ '复用参数' | lang }}</span>
                    <span style="font-size: 13px; color: #898989;">{{ item.create_time }}</span>
                  </div>
                </div>
              </div>
              <div class="message row-ai" style="padding: 15px 30px 20px 30px; width: 862px;">
                <div class="avatar" style="opacity: 0;" />
                <div class="text" style="width: 620px; display: flex; align-items: center; margin-bottom: 15px;">
                  <div class="video" style="margin-right: 20px;" @click="showMusicDetail(item, 'result1')">
                    <musicComponent
                      :music="item"
                      :result="item.result1"
                      @retry="retryMusic(item.music_id)"
                    />
                  </div>
                  <div class="video" @click="showMusicDetail(item, 'result2')">
                    <musicComponent
                      :music="item"
                      :result="item.result2"
                      @retry="retryMusic(item.music_id)"
                    />
                  </div>
                </div>
              </div>
              <div style="clear: both" />
            </div>
          </div>
        </el-scrollbar>
      </div>
      <WelcomeMusic v-if="listType === 'public' && publicList.length === 0" desc="广场空空的~" />
      <WelcomeMusic v-if="listType === 'my' && myList.length === 0" desc="您还没有作品，快去创作吧" />
      <musicDetail v-if="activeMusic" :music="activeMusic" @close="closeMusicDetail" @copyOptions="copyOptions" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { setStorage, getStorage } from '@/utils/auth'
import { getUserBalance } from '@/api/user'
import { submitTask, getHistoryMsg, getPublicList, getMusicResult, getMusicSetting, getMusicDetail } from '@/api/music'

import { musicSuno } from '@/views/index/SubSidebar/Music'
import { MusicComponent, WelcomeMusic, musicDetail } from '@/views/components'
import pages from '@/views/components/Pages'

import fileSaver from 'file-saver'

export default {
  name: 'Music',
  components: { musicSuno, MusicComponent, WelcomeMusic, musicDetail, pages },
  data() {
    return {
      activePlatform: 'pika',
      listType: 'public',
      publicList: [],
      publicPage: 1,
      publicPageSize: 30,
      publicTotalCount: 0,
      myList: [],
      page: 1,
      pagesize: 10,
      activeMusic: null,
      sunoSetting: {},
      tiangongSetting: {},
      user_balance_music: 0,
      is_share: true
    }
  },
  computed: {
    ...mapGetters([
      'user_id',
      'avatar',
      'nickname',
      'priceSetting'
    ]),
    publicPageTotal() {
      return Math.ceil(this.publicTotalCount / this.publicPageSize)
    },
    musicShopIsOpen() {
      if (this.activePlatform === 'suno') {
        return !!this.sunoSetting['shop_open']
      } else if (this.activePlatform === 'tiangong') {
        return !!this.tiangongSetting['shop_open']
      }
      return false
    }
  },
  created() {
    const musicListType = getStorage('musicListType')
    if (musicListType) {
      this.listType = musicListType
    }
    this.getMusicSetting()
    this.getUserBalance()
  },
  methods: {
    switchPlatform(platform) {
      this.activePlatform = platform
      this.publicPage = 1
      this.publicTotalCount = 0
      this.publicList = []
      if (this.musicShopIsOpen) {
        this.getPublicList()
      } else {
        this.switchListType('my')
      }
      this.getHistoryMsg()
    },
    switchListType(type) {
      this.listType = type
      setStorage('musicListType', type)
    },
    getMusicSetting() {
      getMusicSetting().then(res => {
        this.sunoSetting = res.data.suno
        if (this.sunoSetting['is_open']) {
          this.switchPlatform('suno')
        }
      }).catch(err => {
        if (err.message.indexOf('已停用') !== -1) {
          setTimeout(() => {
            this.$router.push({
              name: 'chat'
            })
          }, 500)
        }
      })
    },
    getUserBalance() {
      getUserBalance().then(res => {
        this.user_balance_music = res.data.balance_music
      })
    },
    submitTask() {
      const options = this.getMusicOptions()
      if (options.custom_mode === 0) {
        // 灵感模式，描述必填
        if (!options.prompt) {
          this.$message.error('请输入歌曲描述')
          return
        }
        options.title = ''
        options.tags = ''
      } else {
        // 专业模式+歌曲=歌词必填
        if (!options.make_instrumental && !options.prompt) {
          this.$message.error('请输入自定义歌词')
          return
        }
        if (options.make_instrumental === 1) {
          options.prompt = ''
        }
      }

      submitTask({
        platform: this.activePlatform,
        options: JSON.stringify(options)
      }).then(res => {
        this.switchListType('my')
        this.myList.push({
          state: 0,
          music_id: res.data.music_id,
          action: options.action,
          create_time: res.data.create_time,
          platform: this.activePlatform,
          options: res.data.options,
          result1: '',
          result2: ''
        })
        this.clearOptions()
        setTimeout(() => {
          this.startLoopResult(res.data.music_id)
        }, 1000)
        this.getUserBalance()

        setTimeout(() => {
          this.scrollBottom()
        }, 500)
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        } else if (res.message.indexOf('充值') !== -1) {
          this.showPay()
          setTimeout(() => {
            this.$message.error(res.message)
          }, 500)
        }
      })
    },

    startLoopResult(music_id) {
      setTimeout(() => {
        getMusicResult({
          music_id: music_id
        }).then(res => {
          const state = res.data.state
          if (state === -1) {
            return
          }
          if (state === 0) {
            this.startLoopResult(music_id)
            return
          }
          if (state === 1 || state === 2) {
            var lists = this.myList
            lists.forEach((item, index) => {
              if (item.music_id === music_id) {
                lists[index].state = state
                if (state === 1) {
                  lists[index].result1 = res.data.result1
                  lists[index].result2 = res.data.result2
                } else if (state === 2) {
                  lists[index].errmsg = res.data.message
                }
              }
            })
            this.myList = lists
            setTimeout(() => {
              this.getUserBalance()
            }, 300)
          }
        }).catch(() => {
          this.startLoopResult(music_id)
        })
      }, 10000)
    },

    retryMusic(music_id) {
      submitTask({
        music_id: music_id
      }).then(res => {
        var lists = this.myList
        lists.forEach((item, index) => {
          if (item.music_id === music_id) {
            lists[index].state = 0
            lists[index].errmsg = ''
            setTimeout(() => {
              this.startLoopResult(music_id)
            }, 1000)
          }
        })
        this.myList = lists
        this.getUserBalance()
      })
    },
    getHistoryMsg() {
      getHistoryMsg({
        platform: this.activePlatform,
        page: this.page,
        pagesize: this.pagesize
      }).then(res => {
        if (res.data.length > 0) {
          var lists = res.data
          if (lists) {
            lists.forEach((item, index) => {
              if (item.state === 0 || item.state === 3) {
                this.startLoopResult(item.music_id)
              }
            })
          }
          this.myList = lists
        }
        setTimeout(() => {
          this.scrollBottom()
        }, 500)
      })
    },
    getPublicList() {
      getPublicList({
        page: this.publicPage,
        pagesize: this.publicPageSize,
        platform: this.activePlatform
      }).then(res => {
        this.publicList = res.data.list
        this.publicTotalCount = res.data.count

        this.$nextTick(() => {
          if (this.$refs['publicList']) {
            const container = this.$refs['publicList'].wrap
            setTimeout(() => {
              container.scrollTop = 0
            }, 200)
          }
        })
      })
    },
    changePublicPage(page) {
      this.publicPage = page
      this.getPublicList()
    },
    getMusicOptions() {
      let options = {}
      if (this.$refs.musicSetting) {
        options = this.$refs.musicSetting.getMusicOptions()
      }
      return options
    },
    clearOptions() {
      if (this.$refs.musicSetting) {
        this.$refs.musicSetting.clearMusicOptions()
      }
    },
    copyOptions(music_id) {
      this.closeMusicDetail()
      getMusicDetail({
        type: 'my',
        music_id: music_id
      }).then(res => {
        var options = res.data.options
        options.action = 'generate'
        this.setMusicOptions(options)
      }).catch(err => {
        if (err.message.indexOf('请登录') !== -1) {
          this.showLogin()
        }
      })
    },
    scrollBottom() {
      this.$nextTick(() => {
        if (this.$refs['myList']) {
          const container = this.$refs['myList'].wrap
          setTimeout(() => {
            container.scrollTop = container.scrollHeight
          }, 200)
        }
      })
    },
    showLogin() {
      this.$emit('showLogin')
    },
    showPay() {
      if (this.user_id) {
        if (this.priceSetting.music_vip) {
          this.$emit('showPay', 'vip')
        } else {
          this.$emit('showPay', 'point')
        }
      } else {
        this.$emit('showLogin')
      }
    },
    showMusicDetail(music, field) {
      music.result = music[field]
      this.activeMusic = music
    },
    closeMusicDetail() {
      this.activeMusic = null
    },
    setMusicOptions(options) {
      this.$refs.musicSetting.setMusicOptions(options)
    }
  }
}
</script>
<style lang="scss" scoped>
.row-user {
  .text {
    p {
      margin: 0;
      color: #606468;
      line-height: 28px;
      .label {
        margin-right: 5px;
        //font-weight: bold;
      }
    }
  }
}
</style>
