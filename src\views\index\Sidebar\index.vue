<template>
  <div>
    <div class="module-list">
      <div class="avatar" @click="showUserInfo">
        <img :src="avatar || '/static/img/no_avatar.png'" :title="nickname || $lang('无昵称')">
      </div>
      <div class="module-item" :class="{active: module === 'chat'}" @click="toModule('chat')">
        <svg-icon class="icon" icon-class="ic_mod_chat" style="font-size: 26px;" />
        <span>{{ '对话' | lang }}</span>
      </div>
      <div v-if="writeIsOpen" class="module-item" :class="{active: module === 'write'}" @click="toModule('write')">
        <svg-icon class="icon" icon-class="ic_mod_write" style="font-size: 28px;" />
        <span>{{ '创作' | lang }}</span>
      </div>
      <div v-if="cosplayIsOpen" class="module-item" :class="{active: module === 'cosplay'}" @click="toModule('cosplay')">
        <svg-icon class="icon" icon-class="ic_mod_cosplay" style="font-size: 27px;" />
        <span>{{ '模拟' | lang }}</span>
      </div>
      <div v-if="drawIsOpen" class="module-item" :class="{active: module === 'draw'}" @click="toModule('draw')">
        <svg-icon class="icon" icon-class="ic_mod_draw" style="font-size: 23px;" />
        <span>{{ '绘画' | lang }}</span>
      </div>
      <div v-if="videoIsOpen" class="module-item" :class="{active: module === 'video'}" @click="toModule('video')">
        <svg-icon class="icon" icon-class="ic_mod_video" style="font-size: 23px;" />
        <span>{{ '视频' | lang }}</span>
      </div>
      <div v-if="musicIsOpen" class="module-item" :class="{active: module === 'music'}" @click="toModule('music')">
        <svg-icon class="icon" icon-class="ic_mod_music" style="font-size: 23px;" />
        <span>{{ '音乐' | lang }}</span>
      </div>
      <!-- <div v-if="pkIsOpen" class="module-item" :class="{active: module === 'pk'}" @click="toModule('pk')">
        <svg-icon class="icon" icon-class="ic_mod_pk" style="font-size: 25px;" />
        <span>{{ '擂台' | lang }}</span>
      </div>-->
      <div v-if="appsIsOpen" class="module-item" :class="{active: module === 'apps'}" @click="toModule('apps')">
        <svg-icon class="icon" icon-class="ic_mod_app" style="font-size: 25px;" />
        <span>{{ '更多' | lang }}</span>
      </div>
      <div class="menu-bottom">
        <!--<div class="module-item" @click="toDoc('help')">
          <svg-icon class="icon" icon-class="ic_doc" style="font-size: 24px;" />
          <span>{{ '教程' | lang }}</span>
        </div>-->
        <!--<div class="module-item" @click="toDoc('help')">
          <svg-icon class="icon" icon-class="ic_share" style="font-size: 24px;" />
          <span style="font-size: 12px;">{{ '推广' | lang }}</span>
        </div>-->
        <div v-if="theme === 'light'" class="module-item" :title="'切换为夜间模式' | lang" @click="setTheme('dark')">
          <svg-icon class="icon" icon-class="ic_dark" style="font-size: 18px;" />
          <span style="font-size: 12px;">{{ '夜间' | lang }}</span>
        </div>
        <div v-if="theme === 'dark'" class="module-item" :title="'切换为日间模式' | lang" @click="setTheme('light')">
          <svg-icon class="icon" icon-class="ic_light" style="font-size: 22px;" />
          <span style="font-size: 12px;">{{ '日间' | lang }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getStorage } from '@/utils/auth'

export default {
  data() {
    return {
      isLogin: false,
      page: 1,
      pagesize: 20,
      module: ''
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'user_id',
      'avatar',
      'nickname',
      'vip_expire_time',
      'balance',
      'theme',
      'writeIsOpen',
      'cosplayIsOpen',
      'drawIsOpen',
      'videoIsOpen',
      'musicIsOpen',
      'pkIsOpen',
      'batchIsOpen',
      'novelIsOpen',
      'teamIsOpen',
      'mindIsOpen'
    ]),
    appsIsOpen() {
      return this.pkIsOpen || this.batchIsOpen || this.novelIsOpen || this.mindIsOpen
    },
  },
  mounted() {
    this.getUserInfo()
    this.getSystemInfo()
  },
  methods: {
    toModule(name) {
      this.module = name
      if (name === 'apps') {
        name = getStorage('app')
        if (!name) {
          name = 'pk'
        }
        this.$emit('switchModule', 'apps', name)
      } else {
        this.$emit('switchModule', name)
      }
      this.$router.push({
        name: name
      }).catch(err => {})
    },
    getSystemInfo() {
      this.$store.dispatch('user/getSystemInfo').then(() => {
        const routeName = this.$route.name.toLowerCase()
        if (['pk', 'batch', 'novel', 'team', 'mind', 'doc'].includes(routeName)) {
          this.module = 'apps'
          this.$emit('switchModule', 'apps', routeName)
        } else {
          this.module = routeName
          if (routeName === 'write' || routeName === 'cosplay') {
            this.$emit('switchModule', routeName, this.$route.query.id)
          } else {
            this.$emit('switchModule', routeName)
          }
        }
        this.$forceUpdate()
      })
    },
    showUserInfo() {
      if (this.user_id) {
        this.$emit('showUserInfo')
      } else {
        this.$emit('showLogin')
      }
    },
    getUserInfo() {
      this.$store.dispatch('user/getInfo')
    },
    setTheme(theme) {
      this.$store.dispatch('user/setTheme', theme)
    },
    showLogin() {
      this.$emit('showLogin')
    },
    showPay(type) {
      if (this.user_id) {
        this.$emit('showPay', type);
      } else {
        this.showLogin()
      }
    }
  }
}
</script>
