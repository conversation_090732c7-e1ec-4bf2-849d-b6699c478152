<template>
  <div>
    <div class="module-header">
      <div class="module-title">
        <div class="btn-group-add" @click="editGroup(0)">
          <i class="icon el-icon-plus" />
          <span>{{ '创建新会话' | lang }}</span>
        </div>
      </div>
    </div>
    <div class="module-body">
      <el-scrollbar wrap-class="scrollbar-wrapper">
        <div class="group-chat">
          <div v-for="(item, index) in groupList" class="group-item" :class="{active: item.id === activeGroupId}">
            <div class="group-title" @click="changeGroup(item.id)">
              <i class="icon el-icon-chat-dot-square" />
              <span>{{ item.title }}</span>
            </div>
            <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <i class="btn-dropdown el-icon-more" />
                  </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native.stop="editGroup(item.id)">{{ '编辑' | lang }}</el-dropdown-item>
                <el-dropdown-item class="text-danger" @click.native.stop="delGroup(item.id)">{{ '删除' | lang }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <group-edit v-if="group" :group="group" @close="closeGroupEdit" @submit="saveGroup" />
  </div>
</template>

<script>
import { getGroupList, getGroup, saveGroup, delGroup } from '@/api/group'
import { getStorage } from '@/utils/auth'
import groupEdit from './groupEdit'

export default {
  components: { groupEdit },
  data() {
    return {
      groupList: [],
      group: null,
      activeGroupId: null
    }
  },
  mounted() {
    this.getGroupList(true)
  },
  methods: {
    getGroupList(change = false) {
      getGroupList({ page: this.page, pagesize: this.pagesize }).then(res => {
        this.groupList = res.data.list
        if (change && res.data.list.length > 0) {
          this.changeGroup(res.data.list[0].id)
        }
      })
    },
    editGroup(group_id = 0) {
      if (group_id) {
        getGroup({
          id: group_id
        }).then(res => {
          this.group = res.data
        }).catch(res => {
          if (res.errno === 403) {
            this.$emit('showLogin')
          }
        })
      } else {
        this.group = {
          title: this.$lang('新的会话')
        }
      }
    },
    closeGroupEdit() {
      this.group = null
    },
    saveGroup(group) {
      saveGroup(group).then(res => {
        if (!this.group.id) {
          this.page = 1
          this.getGroupList(true)
        } else {
          for (var i in this.groupList) {
            if (this.groupList[i].id === group.id) {
              this.groupList[i].title = group.title
              break
            }
          }
        }
        this.$forceUpdate()

        this.$message.success(res.message)
        this.closeGroupEdit()
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    delGroup(group_id) {
      this.$confirm('删除后不可恢复，确认删除吗?', '提示', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delGroup({
          id: group_id
        }).then(res => {
          for (var i in this.groupList) {
            if (this.groupList[i].id === group_id) {
              this.groupList.splice(i, 1)
              this.$forceUpdate()
              break
            }
          }
          this.$message.success(res.message)
          this.getGroupList(group_id === this.activeGroupId)
        }).catch(res => {
          if (res.errno === 403) {
            this.$emit('showLogin')
          }
        })
      })
    },
    changeGroup(group_id) {
      this.activeGroupId = group_id
      this.$emit('switchModule', 'chat', group_id)
    },
    getActiveId() {
      return this.activeGroupId
    }
  }
}
</script>
