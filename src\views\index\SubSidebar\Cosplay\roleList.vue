<template>
  <div>
    <div class="module-header">
      <div class="search">
        <el-input v-model="searchKeyword" :placeholder="'搜索角色' | lang" prefix-icon="el-icon-search" size="large" :clearable="true" autocomplete />
      </div>
    </div>
    <div class="module-body">
      <div class="group-cosplay fixed-topic">
        <div v-if="activeTopic" class="topic-title" :class="{is_collapse: activeTopic.collapse}" @click="doCollapse(activeTopic.id)">
          <span>{{ activeTopic.title }}</span>
          <i><svg-icon icon-class="ic_collapse" class="collapse" /></i>
        </div>
      </div>
      <el-scrollbar ref="scroll" wrap-class="scrollbar-wrapper">
        <div v-for="topic in topicList" :key="topic.id" class="group-cosplay">
          <div v-if="topic.roles && topic.roles.length > 0 && !topic.hidden" class="topic-title" :class="{is_collapse: topic.collapse}" :ref="'topic' + topic.id" @click="doCollapse(topic.id)">
            <span>{{ topic.title }}</span>
            <i><svg-icon icon-class="ic_collapse" class="collapse" /></i>
          </div>
          <div v-if="topic.roles && topic.roles.length > 0 && !topic.hidden" class="role-list" :style="'height: ' + (topic.collapse ? 0 : topic.listHeight) + 'px;'">
            <div v-for="role in topic.roles" :key="role.id" class="role-item" :class="{active: activeRole && activeRole.id === role.id, hidden: role.hidden}" @click="changeRole(role)">
              <div class="thumb">
                <img v-if="role.thumb" :src="role.thumb" alt="">
                <img v-else src="/static/img/avatar_cosplay.png" alt="">
              </div>
              <div style="width: 134px;">
                <div class="role-title">{{ role.title }}</div>
                <div class="role-ops">
                  <div class="op-item" :title="'点击量' | lang">
                    <svg-icon class="icon" icon-class="ic_view" />
                    {{ role.views }}
                  </div>
                  <div class="op-item" :title="'使用量' | lang">
                    <svg-icon class="icon" icon-class="ic_usage" />
                    {{ role.usages }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { getAllRoles } from '@/api/cosplay'

export default {
  data() {
    return {
      topicList: [],
      activeTopic: null,
      activeRole: null,
      scrollTop: 0,
      searchKeyword: ''
    }
  },
  watch: {
    searchKeyword() {
      this.filterList()
    }
  },
  mounted() {
    this.getAllRoles()
    this.$nextTick(() => {
      setTimeout(() => {
        this.onScroll()
      }, 300)
    })
  },
  methods: {
    filterList() {
      const keyword = this.searchKeyword.toLowerCase()
      const topicList = this.topicList
      topicList.forEach((topic, idx1) => {
        // 确保topic.roles存在且为数组
        if (!topic.roles || !Array.isArray(topic.roles)) {
          topic.roles = []
        }

        if (topic.roles.length > 0) {
          let topicHidden = true
          let count = 0
          topic.roles.forEach((role, idx2) => {
            // 确保role对象存在
            if (role) {
              if (keyword && role.title.toLowerCase().indexOf(keyword) === -1) {
                role.hidden = true
              } else {
                count++
                role.hidden = false
                topicHidden = false
              }
            }
          })
          topic.hidden = topicHidden
          topic.listHeight = count * 80
          topicList[idx1] = topic
        } else {
          // 如果没有roles，隐藏整个topic
          topic.hidden = true
          topicList[idx1] = topic
        }
      })
      this.topicList = topicList
      this.$forceUpdate()
    },
    onScroll() {
      const scrollEl = this.$refs.scroll.wrap
      scrollEl.onscroll = () => {
        this.handleScroll()
      }
    },
    handleScroll() {
      const scrollEl = this.$refs.scroll.wrap
      const topicList = this.topicList
      for (let i = topicList.length - 1; i >= 0; i--) {
        if (topicList[i].hidden) {
          continue
        }
        if (this.$refs['topic' + topicList[i].id][0].offsetTop <= scrollEl.scrollTop) {
          this.activeTopic = topicList[i]
          return
        }
      }
    },
    getAllRoles() {
      getAllRoles().then(res => {
        const topicList = res.data
        const activeRoleId = parseInt(this.$route.query.id)
        let scrollTop = 0
        topicList.forEach((topic, index) => {
          // 确保topic.roles存在且为数组
          if (!topic.roles || !Array.isArray(topic.roles)) {
            topic.roles = []
          }

          // 初始化每个role的hidden属性
          topic.roles.forEach((role) => {
            role.hidden = false
          })

          // 正确计算listHeight，考虑可能隐藏的元素
          topic.listHeight = topic.roles.length * 80
          topic.hidden = false

          if (activeRoleId) {
            topic['roles'].forEach(role => {
              scrollTop += 80
              if (role.id === activeRoleId) {
                this.activeRole = role
                this.scrollTop = scrollTop - 200
                this.$emit('changeRole', role.id)
              }
            })
          }
        })
        this.topicList = topicList
        this.$nextTick(() => {
          if (this.activeRole && this.scrollTop) {
            this.$refs.scroll.wrap.scrollTop = this.scrollTop
            this.handleScroll()
          }
        })
      })
    },
    changeRole(role) {
      this.activeRole = role
      this.$emit('switchModule', 'cosplay', role.id)
    },
    getActiveId() {
      return this.activeRole ? this.activeRole.id : 0
    },
    doCollapse(id) {
      const topicList = this.topicList
      let thisTopic = null
      const scrollEl = this.$refs.scroll.wrap

      // 折叠
      topicList.forEach((topic, index) => {
        if (topic.id === id) {
          topicList[index].collapse = !topic.collapse
          thisTopic = this.$refs['topic' + topic.id][0]
        }
      })
      this.topicList = topicList
      this.$forceUpdate()

      this.$nextTick(() => {
        // 点击吸顶分类, 滚动到合适的位置
        if (this.activeTopic && id === this.activeTopic.id) {
          setTimeout(() => {
            scrollEl.scrollTop = thisTopic.offsetTop
          }, 500)
        }
      })
    }
  }
}
</script>

