<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="box-draw-setting">
      <div class="setting-row">
        <prompt ref="prompt" type="mj" style="width: 100%;"></prompt>
      </div>
      <div class="setting-row">
        <div class="setting-item col-1">
          <div class="header">
            <span>✳ {{ '参考图片' | lang }}</span>
            <div style="display: flex; align-items: center;">
              <el-tooltip effect="dark" content="设置参考图，尺寸限1500x1500像素以内" placement="bottom">
                <i class="el-icon-warning-outline tips" />
              </el-tooltip>
            </div>
          </div>
          <div class="options">
            <el-upload
              class="avatar-uploader"
              action=""
              :http-request="uploadImage"
              :show-file-list="false"
              :multiple="false"
              title="点击上传图片"
            >
              <div v-if="options.image" class="image">
                <img :src="options.image" />
                <span class="del" title="移除图片" @click.stop="removeImage">
                  <i class="el-icon-close"></i>
                </span>
              </div>
              <i v-else class="el-icon-plus avatar-uploader-icon" style="width: 80px; height: 80px; line-height:80px;" />
            </el-upload>
            <div style="clear:both;" />
          </div>
        </div>
        <div class="setting-item col-3">
          <div class="header">
            <span>✳ {{ '画布比例' | lang }}</span>
            <el-tooltip effect="dark" content="指定生成图像的宽高比" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options" style="display: flex; justify-content: space-around;">
            <div v-for="item in sizes" class="size-item" :class="{active: options.ar === item.width + ':' + item.height}" @click="setSize(item.width, item.height)">
              <div v-if="item.width>=item.height" class="size-block" :style="'width: 36px; height: ' + parseInt((item.height/item.width) * 36) + 'px;'" />
              <div v-else class="size-block" :style="'height: 36px; width: ' + parseInt((item.width/item.height) * 36) + 'px;'" />
              <span>{{ item.title }}</span>
            </div>
            <div style="clear:both;" />
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ '参考图权重 --iw' | lang }}</span>
            <el-tooltip effect="dark" content="设置生成图片时垫图的权重，值越大越像垫图，取值范围0.5-2， 默认值1" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <div style="padding-bottom: 10px;">
              <el-slider
                v-model="options.iw"
                :min="iw.min"
                :max="iw.max"
                :marks="iw.marks"
                :format-tooltip="divide10"
              />
            </div>
            <div style="clear:both;" />
          </div>
        </div>
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ '图片质量 --q' | lang }}</span>
            <el-tooltip effect="dark" :content="q.explain" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <div style="padding-bottom: 10px;">
              <el-slider
                v-model="options.q"
                :min="q.min"
                :max="q.max"
                :marks="q.marks"
                :format-tooltip="divide10"
              />
            </div>
            <div style="clear:both;" />
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ '风格化值 --s' | lang }}</span>
            <el-tooltip effect="dark" :content="s.explain" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <div style="padding-bottom: 10px;">
              <el-slider
                v-model="options.s"
                :min="s.min"
                :max="s.max"
                :marks="s.marks"
              />
            </div>
            <div style="clear:both;" />
          </div>
        </div>
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ '混乱值 --c' | lang }}</span>
            <el-tooltip effect="dark" :content="c.explain" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <div>
              <el-slider
                v-model="options.c"
                :min="c.min"
                :max="c.max"
              />
            </div>
            <div style="clear:both;" />
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ '排除内容 --no' | lang }}</span>
            <el-tooltip effect="dark" content="指定图片内要排除的生成内容" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-input v-model="options.no" type="text" :placeholder="'请输入排除的内容' | lang" size="normal" style="width: 320px;" />
            <div style="clear:both;" />
          </div>
        </div>
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ '种子 --seed' | lang }}</span>
            <el-tooltip effect="dark" content="种子用于指定生成效果，可以用于生成套图，保障生成的一系列图片保持同一种风格" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-input v-model="options.seed" type="text" :placeholder="'请输seed种子编号' | lang" size="normal" style="width: 320px;" />
            <div style="clear:both;" />
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ 'niji渲染模型 --niji' | lang }}</span>
            <el-tooltip effect="dark" content="niji是动漫模型" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-radio-group v-model="options.niji" size="small">
              <el-radio-button label="">默认</el-radio-button>
              <el-radio-button label="6">niji6模型</el-radio-button>
              <el-radio-button label="5">niji5模型</el-radio-button>
              <el-radio-button label="4">niji4模型</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ '风格 --style' | lang }}</span>
            <el-tooltip effect="dark" content="指定midjourney的风格，一般用于设置niji风格" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-radio-group v-model="options.style" size="small">
              <el-radio-button label="">默认</el-radio-button>
              <el-radio-button label="expressive">expressive-3d</el-radio-button>
              <el-radio-button label="cute">cute-可爱</el-radio-button>
              <el-radio-button label="raw">raw</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>

      <div class="setting-row">
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ 'MJ渲染模型 --v' | lang }}</span>
            <el-tooltip effect="dark" content="指定midjourney的渲染模型" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-radio-group v-model="options.v" size="small">
              <el-radio-button label="">默认</el-radio-button>
              <el-radio-button label="6.0">v6</el-radio-button>
              <el-radio-button label="5.2">v5.2</el-radio-button>
              <el-radio-button label="5.1">v5.1</el-radio-button>
              <el-radio-button label="5">v5</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="setting-item col-2">
          <div class="header">
            <span>✳ {{ '拼贴图模式 --tile' | lang }}</span>
            <el-tooltip effect="dark" content="用于生成无限拼贴图" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-radio-group v-model="options.tile" size="small">
              <el-radio-button :label="0">不开启</el-radio-button>
              <el-radio-button :label="1">开启</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
import prompt from './prompt'
import { uploadImage } from '@/api/upload'
export default {
  components: { prompt },
  data() {
    return {
      defaultOptions: {
        ar: '1:1',
        iw: 10,
        s: 100,
        q: 10,
        c: 0,
        no: '',
        seed: '',
        niji: '',
        style: '',
        v: '',
        tile: 0,
        image: '',
        words: []
      },
      options: {},
      sizes: [
        {
          width: 1,
          height: 1,
          title: '1:1',
          desc: '默认值'
        },
        {
          width: 16,
          height: 9,
          title: '16:9',
          desc: '电脑端'
        },
        {
          width: 9,
          height: 16,
          title: '9:16',
          desc: '手机端'
        },
        {
          width: 3,
          height: 2,
          title: '3:2',
          desc: '相机'
        },
        {
          width: 4,
          height: 3,
          title: '4:3',
          desc: '高清'
        },
        {
          width: 3,
          height: 4,
          title: '3:4',
          desc: '高清'
        }
      ],
      iw: {
        min: 5,
        max: 20,
        marks: {
          10: '默认1'
        },
        explain: '设置生成图片时垫图的权重，值越大越像垫图，取值范围0.5-2， 默认值1'
      },
      s: {
        min: 0,
        max: 1000,
        marks: {
          100: '默认100'
        },
        explain: '设置生成图片时的风格化程度，值越大，风格化的程度越高，取值范围0-1000， 默认值100'
      },
      q: {
        min: 3,
        max: 50,
        marks: {
          10: '默认1'
        },
        explain: '设置图片的质量，越大质量越高，取值范围0.3-5，默认值1'
      },
      c: {
        min: 0,
        max: 100,
        explain: '本参数会控制生成4张图的差别， 值越大，生成4张图的区别越大，值越小,生成的4张图越接近，取值范围0-100， 默认值0'
      }
    }
  },
  created() {
    this.options = JSON.parse(JSON.stringify(this.defaultOptions))
  },
  methods: {
    getDrawOptions() {
      const options = JSON.parse(JSON.stringify(this.options))
      options.words = this.$refs['prompt'].getWordsPicked()
      if (options.iw) {
        options.iw = this.divide10(options.iw);
      }
      if (options.q) {
        options.q = this.divide10(options.q)
      }
      return options
    },
    clearDrawOptions() {
      this.$refs.prompt.clearPicked()
      this.options.image = ''
    },
    setDrawOptions(options) {
      if (options.iw) {
        options.iw = options.iw * 10
      }
      if (options.q) {
        options.q = options.q * 10
      }
      options = Object.assign(this.defaultOptions, options)
      this.options = JSON.parse(JSON.stringify(options))
      if (options.words) {
        this.$refs.prompt.setWords(options.words)
      }
    },
    showPay(type) {
      this.$emit('showPay', type)
    },
    divide10(num) {
      return num / 10
    },
    setSize(width, height) {
      this.options.ar = width + ':' + height
    },
    uploadImage(item) {
      var form = new FormData()
      form.append('image', item.file)
      if (item.data) {
        form.append('data', JSON.stringify(item.data))
      }
      uploadImage(form).then(res => {
        this.options.image = res.data.path
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    removeImage() {
      this.options.image = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.draw-setting .module-body .box-draw-setting {
  .size-item {
    width: 66px;
    height: 85px;
  }
}
</style>
