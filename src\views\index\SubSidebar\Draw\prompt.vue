<template>
  <div>
    <div class="setting-item box-prompt">
      <div class="header" style="margin-top: 10px;">
        <span>✳ {{ '画面描述' | lang }}</span>
        <el-button type="default" size="mini" @click="clearPicked">重置</el-button>
      </div>
      <div v-if="wordsCate.length > 0" class="words-cate">
        <div v-for="(item,index) in wordsCate" class="item" @click="showWordsPicker(index, item.id)">
          <span>{{ item.title }}</span>
          <span v-if="pickedCount(item.id) > 0" class="count">{{ pickedCount(item.id) }}</span>
        </div>
        <div style="clear: both;"></div>
      </div>
      <div class="words-input">
        <el-input
          ref="messageInput"
          v-model="wordInput"
          :placeholder="'输入自定义画面描述' | lang"
          type="textarea"
          :autofocus="true"
          :autosize="{ minRows: 2, maxRows: 8 }"
          maxlength="1000"
          @keyup.enter.native="onEnter"
        />
        <el-button
          class="btn-add"
          type="primary"
          plain
          size="small"
          @click="addToPicked"
        >
          <i class="el-icon-bottom" /> 添加到描述
        </el-button>
      </div>
      <div v-if="wordsPicked && wordsPicked.length > 0" class="words-list">
        <el-tag
          v-for="(item,index) in wordsPicked"
          :key="index"
          class="item"
          type="primary"
          size="large"
          closable
          :disable-transitions="false"
          @close="removePicked(index)"
        >
          <span v-if="type === 'mj'" class="weight" :title="'比重: ' + item.weight" @click="showWordWeight(index)">{{ item.weight }}</span>
          {{ item.text.length > 20 ? item.text.substr(0, 20) + '...' : item.text }}
        </el-tag>
        <div style="clear: both;" />
      </div>
      <div style="height: 1px; clear: both;" />

    </div>
    <div v-if="activeWord">
      <el-dialog
        custom-class="my-dialog"
        :title="'调整比重' | lang"
        width="400px"
        :visible="true"
        :close-on-click-modal="true"
        :append-to-body="true"
        :before-close="closeWordWeight"
      >
        <el-alert type="info" show-icon :closable="false" title="比重取值范围1-20，默认1" />
        <div style="padding: 30px 10px 20px 15px;">
          <el-slider
            v-model="activeWord.weight"
            :min="1"
            :max="20"
            show-input
          />
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button type="default" icon="el-icon-close" size="small" @click="closeWordWeight">{{ '取 消' | lang }}</el-button>
          <el-button type="primary" icon="el-icon-check" size="small" @click="setWordWeight">{{ '确 定' | lang }}</el-button>
        </span>
      </el-dialog>
    </div>
    <words-picker :pcate-index="wordsPickerActivePcateIndex" :cates="wordsCate" :picked="wordsPicked" @close="closeWordsPicker" @picker="setWords" @getWordsList="getWordsList" @setWords="setWords" />
  </div>
</template>

<script>
import { getWordsCate, getWordsList } from '@/api/draw'
import { wordsPicker } from '@/views/index/SubSidebar/Draw'

export default {
  components: { wordsPicker },
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      lists: [],
      wordInput: '',
      page: 1,
      pagesize: 10,
      drawSetting: {},
      wordsCate: [],
      activeWordIndex: null,
      activeWord: null,
      wordsPickerActivePcateIndex: -1,
      wordsPickerActivePcateId: 0,
      wordsPicked: []
    }
  },
  created() {
    this.getWordsCate()
  },
  methods: {
    onEnter(e) {
      if (e.ctrlKey) {
        this.addToPicked()
      }
      return false
    },
    getWordsCate() {
      getWordsCate().then(res => {
        this.wordsCate = res.data
      })
    },
    getWordsList(pcateIndex, pcateId) {
      getWordsList({
        pcate: pcateId
      }).then(res => {
        const wordsCate = JSON.parse(JSON.stringify(this.wordsCate))
        wordsCate[pcateIndex].wordsList = res.data.list
        wordsCate[pcateIndex].wordsCount = res.data.count
        this.wordsCate = wordsCate
      })
    },
    addToPicked() {
      const word = this.trim(this.wordInput)
      if (word) {
        this.wordsPicked.push({
          text: word,
          weight: 1
        })
        this.wordInput = ''
      }
    },
    removePicked(index) {
      this.wordsPicked.splice(index, 1)
    },
    clearPicked() {
      this.wordsPicked = []
    },
    showWordsPicker(pcateIndex, pcateId) {
      this.wordsPickerActivePcateIndex = pcateIndex
      this.wordsPickerActivePcateId = pcateId
    },
    closeWordsPicker() {
      this.wordsPickerActivePcateIndex = -1
      this.wordsPickerActivePcateId = 0
    },
    setWords(words) {
      this.wordsPicked = words
      this.closeWordsPicker()
    },
    getWordsPicked() {
      return this.wordsPicked
    },
    showWordWeight(index) {
      this.activeWordIndex = index
      this.activeWord = JSON.parse(JSON.stringify(this.wordsPicked[index]))
    },
    closeWordWeight() {
      this.activeWordIndex = null
      this.activeWord = null
    },
    setWordWeight() {
      this.wordsPicked[this.activeWordIndex] = this.activeWord
      this.closeWordWeight()
    },
    wordWeightFormat(value) {
      return '比重：' + value
    },
    pickedCount(id) {
      let count = 0
      if (this.wordsPicked) {
        this.wordsPicked.forEach(item => {
          if (item.pcate === id) {
            count++
          }
        })
      }
      return count
    },
    trim(str) {
      if (str) {
        str = str.replace(/(^\s*)|(\s*$)/g, '')
      }
      return str
    },
    showLogin() {
      this.$emit('showLogin')
    },
    showPay() {
      if (this.user_id) {
        this.$emit('showPay', 'point')
      } else {
        this.$emit('showLogin')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.box-prompt {
  width: 100%;
  box-sizing: border-box;
  transition: background 0.1s ease-in-out;
  z-index: 9;

  .words-cate {
    padding: 0 0 12px 0;
    .item {
      margin-left: 0;
      margin-right: 8px;
      margin-bottom: 8px;
      padding: 0 15px;
      line-height: 35px;
      height: 35px;
      background-color: #e2e6e4;
      color: #656565;
      font-size: 14px;
      font-weight: normal;
      border-radius: 5px;
      cursor: pointer;
      float: left;
      display: flex;
      justify-content: center;
      align-items: center;
      &:hover {
        background: #d4dad6;
      }
      .count {
        display: inline-block;
        width: 18px;
        height: 18px;
        line-height: 18px;
        text-align: center;
        background-color: #10a37f;
        color: #fff;
        margin-left: 4px;
        border-radius: 50%;
        font-size: 12px;
      }
    }
  }
  .words-input {
    position: relative;
    .btn-add {
      position: absolute;
      right: 8px;
      bottom: 8px;
      height: 36px;
      transition: background 0.1s ease-in-out, color 0.1s ease-in-out;
      letter-spacing: 1px;
    }
  }

  .words-list {
    margin: 20px 0 0 0;
    max-height: 302px;
    overflow: auto;
    .item {
      margin-right: 8px;
      margin-bottom: 8px;
      position: relative;
      float: left;
      border-radius: 5px;
      overflow: hidden;
      display: flex;
      align-items: center;
      border: 1px solid #10a37f;
      font-size: 14px !important;

      .weight {
        display: block;
        background: #10a37f;
        border-radius: 4px;
        color: #fff;
        width: 18px;
        height: 18px;
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        cursor: pointer;
        margin-right: 6px;
        &:hover {
          opacity: 0.6;
        }
      }
    }
  }
}
</style>
