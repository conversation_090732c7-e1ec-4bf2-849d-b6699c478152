<template>
  <div class="picker-container">
    <el-drawer
      title=""
      direction="rtl"
      size="calc(100% - 440px)"
      :visible="activePcateId > 0"
      :close-on-click-modal="true"
      :before-close="closeWordsPicker"
    >
      <div class="picker-main">
        <div class="pcate-list" ref="pcateList">
          <el-button class="item" :class="{ active: index === activePcateIndex }" type="primary" :plain="index !== activePcateIndex" size="normal" v-for="(item, index) in cates" @click="changePcate(index, item.id)">
            <span>{{ item.title }}</span>
            <span class="count" v-if="pickedCount('pcate', item.id) > 0">{{ pickedCount('pcate', item.id) }}</span>
          </el-button>
        </div>
        <div class="scate-list" ref="scateList">
          <div class="item" :class="{active: index === activeScateIndex}" type="default" plain size="normal" v-for="(item, index) in scateList" @click="changeScate(index, item.id)">
            <span>{{ item.title }}</span>
            <span class="count" v-if="pickedCount('scate', item.id) > 0">{{ pickedCount('scate', item.id) }}</span>
          </div>
          <div style="clear: both;"></div>
        </div>
        <div class="words-list" v-if="wordsList" ref="wordsList" :style="'top: ' + wordsListTop + 'px; bottom: ' + wordsListBottom + 'px;'">
          <div class="item" :class="{active: pickedIds.includes(item.id)}" v-for="(item, index) in wordsList" @click="pickWord(item)">{{ item.title }}</div>
          <div style="clear: both"></div>
        </div>
        <div class="picker-footer">
          <div class="words-picked" v-if="wordsPicked && wordsPicked.length > 0" ref="wordsPicked">
            <el-tag
              class="item"
              type="primary"
              size="large"
              v-for="(item, index) in wordsPicked"
              :key="index"
              closable
              :disable-transitions="false"
              @close="delWord(index)">
              {{ item.text.length > 20 ? item.text.substr(0, 20) + '...' : item.text }}
            </el-tag>
          </div>
          <div class="footer">
            <el-button type="default" size="normal" @click="closeWordsPicker">取 消</el-button>
            <el-button type="primary" size="normal" @click="setWords">确 定</el-button>
          </div>
        </div>

      </div>
    </el-drawer>
  </div>
</template>

<script>

export default {
  props: {
    cates: {
      type: Array,
      default() {
        return []
      }
    },
    picked: {
      type: Array,
      default() {
        return []
      }
    },
    pcateIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      wordsList: [],
      wordsCount: 0,
      wordsPicked: [],
      pickedIds: [],
      activePcateIndex: -1,
      activePcateId: 0,
      activeScateIndex: -1,
      activeScateId: 0,
      wordsListTop: 0,
      wordsListBottom: 0
    }
  },
  computed: {
    scateList() {
      if (this.activePcateIndex >= 0) {
        return this.cates[this.activePcateIndex].children
      } else {
        return []
      }
    }
  },
  watch: {
    cates: {
      handler() {
        this.makeWordsList()
      },
      deep: true,
      immediate: true
    },
    pcateIndex: {
      handler() {
        if (this.pcateIndex >= 0) {
          this.changePcate(this.pcateIndex, this.cates[this.pcateIndex].id)
        } else {
          this.activePcateIndex = -1
          this.activePcateId = 0
          this.wordsList = []
          this.wordsCount = 0
        }

        // 已设置的提示词
        this.wordsPicked = JSON.parse(JSON.stringify(this.picked))
        this.calcPickedIds()
        this.calcWordsListBottom()
      },
      immediate: true
    }
  },
  created() {

  },
  methods: {
    getWordsList() {
      if (!this.cates[this.activePcateIndex].wordsList) {
        this.$emit('getWordsList', this.activePcateIndex, this.activePcateId)
      }
    },
    makeWordsList() {
      if (this.activePcateIndex < 0) {
        return
      }
      let list = [];
      let wordsList = this.cates[this.activePcateIndex]['wordsList']
      if (!wordsList) {
        this.getWordsList()
        return
      }
      if (this.activeScateId) {
        wordsList.forEach((item, index) => {
          if (item.scate === this.activeScateId) {
            list.push(item)
          }
        })
      } else {
        list = wordsList
      }
      this.wordsList = list
      this.wordsCount = list.length
      this.calcWordsListTop()
    },
    changePcate(index, id) {
      this.activePcateIndex = index
      this.activePcateId = id
      if (this.cates[this.activePcateIndex].children.length > 0) {
        this.activeScateIndex = 0
        this.activeScateId = this.cates[this.activePcateIndex].children[0].id
      } else {
        this.activeScateIndex = -1
        this.activeScateId = 0
      }
      this.makeWordsList()
    },
    changeScate(index, id) {
      this.activeScateIndex = index
      this.activeScateId = id
      this.makeWordsList()
    },
    closeWordsPicker(scate) {
      this.$emit('close')
    },
    setWords() {
      this.$emit('setWords', this.wordsPicked)
    },
    pickWord(item) {
      if (this.pickedIds.includes(item.id)) {
        for (var i = 0; i < this.wordsPicked.length; i++) {
          if (this.wordsPicked[i].id === item.id) {
            this.wordsPicked.splice(i, 1)
            break
          }
        }
      } else {
        this.wordsPicked.push({
          id: item.id,
          pcate: item.pcate,
          scate: item.scate,
          text: item.title,
          weight: 1
        })
      }
      this.calcPickedIds()
      this.calcWordsListTop()
      this.calcWordsListBottom()
    },
    delWord(index) {
      this.wordsPicked.splice(index, 1)
      this.calcPickedIds()
      setTimeout(() => {
        this.calcWordsListTop()
        this.calcWordsListBottom()
      }, 200)
    },
    calcWordsListTop() {
      this.$nextTick(() => {
        this.wordsListTop = this.$refs.pcateList.offsetHeight + this.$refs.scateList.offsetHeight + 20
      })
    },
    calcWordsListBottom() {
      this.$nextTick(() => {
        this.wordsListBottom = 80
        if (this.$refs.wordsPicked) {
          this.wordsListBottom += this.$refs.wordsPicked.offsetHeight
        }
      })
    },
    pickedCount(type, id) {
      let count = 0
      this.wordsPicked.forEach(item => {
        if (item[type] === id) {
          count++
        }
      })
      return count
    },
    calcPickedIds() {
      let ids = []
      this.wordsPicked.forEach(item => {
        if (item.id) {
          ids.push(item.id)
        }
      })
      this.pickedIds = ids
    },
  }
}
</script>
<style lang="scss">
.picker-container {
  .el-drawer__header {
    display: none !important;
  }
}
</style>
<style lang="scss" scoped>
.picker-main {
  padding: 20px;
  position: relative;
  height: 100%;
  overflow: hidden;

  .pcate-list {
    width: 100%;
    .item {
      margin: 0 8px 8px 0;
      line-height: 18px;
      .count {
        display: inline-block;
        margin-left: 8px;
        border-radius: 50%;
        background: #10a37f;
        width: 16px;
        height: 16px;
        line-height: 16px;
        text-align: center;
        color: #fff;
        font-size: 12px;
      }
      &.active .count {
        background: #fff;
        color: #10a37f;
      }
    }
  }
  .scate-list {
    width: 100%;
    padding-top: 8px;
    padding-bottom: 12px;
    .item {
      float:left;
      display: block;
      text-align: center;
      line-height: 32px;
      padding: 2px 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      margin: 0 8px 8px 0;
      cursor: pointer;
      font-size: 14px;
      &:hover {
        border-color: #999;
      }
      &.active {
        border-color: #10a37f;
        color: #10a37f;
      }
      .count {
        display: inline-block;
        margin-left: 10px;
        border-radius: 50%;
        background: #10a37f;
        width: 18px;
        height: 18px;
        line-height: 18px;
        text-align: center;
        color: #fff;
        font-size: 12px;
      }
    }
  }
  .words-list {
    padding: 0 0;
    overflow: hidden;
    overflow-y: scroll;
    position: absolute;
    left: 20px;
    right: 20px;
    .item {
      width: 162px;
      height: 60px;
      background: #eff0f0;
      float: left;
      margin-right: 10px;
      margin-bottom: 10px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px 15px;
      font-size: 15px;
      line-height: 1.4;
      cursor: pointer;
      border: 1px solid #f7f7f8;
      &:hover {
        background: #f2f2f3;
      }
      &.active {
        border-color: #10a37f;
        background: #e3f5f0;
        color: #10a37f;
      }
    }
  }
  .picker-footer {
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    .words-picked {
      width: 100%;
      padding-top: 20px;
      .item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
    .footer {
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

</style>
