<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="box-music-setting">
      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '生成模式' | lang }}</span>
            <el-tooltip effect="dark" content="自动模式只需要写歌曲描述，会自动生成歌曲和歌词，专业模式需要写歌词" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-radio-group v-model="options.custom_mode" size="small">
              <el-radio-button :label="0">灵感模式</el-radio-button>
              <el-radio-button :label="1">专业模式</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '类型' | lang }}</span>
          </div>
          <div class="options">
            <el-radio-group v-model="options.make_instrumental" size="small">
              <el-radio-button :label="0">歌曲</el-radio-button>
              <el-radio-button :label="1">纯音乐</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div v-if="options.custom_mode === 0" class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '歌曲描述（必填）' | lang }}</span>
          </div>
          <div class="options" style="position: relative;">
            <el-input
              ref="messageInput"
              v-model="options.prompt"
              :placeholder="'输入文字描述，自动生成音乐' | lang"
              type="textarea"
              :autofocus="true"
              :autosize="{ minRows: 4, maxRows: 8 }"
              maxlength="200"
              show-word-limit
            />
          </div>
        </div>
      </div>
      <div v-if="options.custom_mode === 1">
        <div class="setting-row">
          <div class="setting-item">
            <div class="header">
              <span>✳ {{ '歌名（选填）' | lang }}</span>
            </div>
            <div class="options">
              <el-input v-model="options.title" type="text" :placeholder="'请输入歌曲名称' | lang" size="normal" style="width: 420px;" />
              <div style="clear:both;" />
            </div>
          </div>
        </div>
        <div class="setting-row">
          <div class="setting-item">
            <div class="header">
              <span>✳ {{ '曲风（选填）' | lang }}</span>
            </div>
            <div class="options" style="position: relative;">
              <el-input
                ref="messageInput"
                v-model="options.tags"
                :placeholder="'比如：摇滚、柔和、轻快、沧桑等' | lang"
                type="textarea"
                :autofocus="true"
                :autosize="{ minRows: 3, maxRows: 8 }"
                maxlength="120"
                show-word-limit
              />
            </div>
          </div>
        </div>
        <div v-if="options.make_instrumental === 0" class="setting-row">
          <div class="setting-item">
            <div class="header">
              <span>✳ {{ '自定义歌词（必填）' | lang }}</span>
              <el-button v-if="writing" type="primary" plain size="mini" title="所有参数恢复默认" @click="stopMakeLyric">停止生成</el-button>
              <el-button v-else type="primary" plain size="mini" title="所有参数恢复默认" @click="showMakeLyric">AI写歌词</el-button>
            </div>
            <div class="options" style="position: relative;">
              <el-input
                ref="messageInput"
                v-model="options.prompt"
                :placeholder="'输入自定义歌词' | lang"
                type="textarea"
                :autofocus="true"
                :autosize="{ minRows: 6, maxRows: 20 }"
                maxlength="1200"
                show-word-limit
              />
            </div>
          </div>
        </div>
      </div>

      <div v-if="makeLyricShow">
        <el-dialog
          custom-class="my-dialog"
          :title="'生成歌词' | lang"
          width="620px"
          :visible="true"
          :close-on-click-modal="false"
          :append-to-body="true"
          :before-close="closeMakeLyric"
        >
          <el-form ref="form" :model="lyricForm" label-width="100px" style="padding: 20px 0;">
            <el-form-item :label="'写作要求' | lang">
              <el-input id="lyric" v-model="lyricForm.prompt" type="textarea" :rows="8" :placeholder="'请输入歌词要求' | lang" size="normal" style="width: 430px;" />
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="default" icon="el-icon-close" size="small" @click="closeMakeLyric">{{ '取 消' | lang }}</el-button>
            <el-button type="primary" icon="el-icon-check" size="small" @click="startMakeLyric">{{ '开始生成' | lang }}</el-button>
          </span>
        </el-dialog>
      </div>

    </div>
  </el-scrollbar>
</template>

<script>
import { getSiteCode, getToken } from '@/utils/auth'

var textStacks = []
var textOutputSi = 0
var fetchCtrl = null
export default {
  data() {
    return {
      defaultOptions: {
        custom_mode: 0,
        make_instrumental: 0,
        title: '',
        prompt: '',
        tags: ''
      },
      options: {},
      lyricForm: {
        prompt: ''
      },
      makeLyricShow: false,
      writing: false
    }
  },
  created() {
    this.options = JSON.parse(JSON.stringify(this.defaultOptions))
  },
  methods: {
    optionChange(name, value) {
      this.$set(this.options, name, value)
    },
    getMusicOptions() {
      return JSON.parse(JSON.stringify(this.options))
    },
    clearMusicOptions() {
      this.options.title = ''
      this.options.prompt = ''
      this.options.tags = ''
    },
    setMusicOptions(options) {
      options = Object.assign(this.defaultOptions, options)
      this.options = JSON.parse(JSON.stringify(options))
    },
    showPay(type) {
      this.$emit('showPay', type)
    },
    showMakeLyric() {
      this.makeLyricShow = true
      let prompt = ''
      if (this.options.title) {
        prompt += '歌名：' + this.options.title + '\n'
      }
      if (this.options.tags) {
        prompt += '曲风：' + this.options.tags + '\n'
      }
      this.lyricForm = {
        prompt: prompt
      }
    },
    closeMakeLyric() {
      this.makeLyricShow = false
    },
    async startMakeLyric() {
      if (this.writing || this.writingText) {
        return
      }
      const loadingText = '正在生成...'
      const lyric = this.options.prompt
      const message = this.lyricForm.prompt
      if (!message) {
        this.$message.error('请输入歌词要求')
        return
      }
      this.options.prompt = loadingText
      document.getElementById('lyric').focus()

      if (textOutputSi) {
        clearInterval(textOutputSi)
        textOutputSi = 0
        textStacks = []
      }

      this.writing = true
      this.scrollLyricBottom()
      this.closeMakeLyric()

      const headers = new Headers()
      headers.append('Content-Type', 'application/json')
      headers.append('X-token', getToken() || '')
      headers.append('X-site', getSiteCode() || '')
      const url = process.env.VUE_APP_BASE_API + '/chat/sendText'
      const data = {
        tool: 'lyric',
        message: message
      }
      fetchCtrl = new AbortController()
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
        signal: fetchCtrl.signal
      })
      if (!response.ok) {
        this.writing = false
        this.$message.error(response.statusText)
        return
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let done = false
      const curAiMsg = ''

      // eslint-disable-next-line require-atomic-updates
      textOutputSi = setInterval(() => {
        if (this.writing) {
          if (textStacks.length > 0) {
            if (this.options.prompt === loadingText) {
              this.options.prompt = ''
            }
            this.options.prompt += textStacks.shift()
            if (this.options.prompt === ' ') {
              this.options.prompt = ''
            }
            this.scrollLyricBottom()
          }
        } else {
          if (textStacks.length > 0) {
            if (this.options.prompt === loadingText) {
              this.options.prompt = ''
            }
            this.options.prompt += textStacks.join('')
            textStacks = []
            this.scrollLyricBottom()
          }
          clearInterval(textOutputSi)
          textOutputSi = 0
          this.scrollLyricBottom()
        }
      }, 25)

      while (!done) {
        this.scrollLyricBottom()
        const { value, done: readerDone } = await reader.read()
        if (value) {
          const char = decoder.decode(value)
          if (char === '\n' && curAiMsg.endsWith('\n')) {
            continue
          }
          if (char) {
            if (char.indexOf('[error]') !== -1) {
              this.writing = false
              this.options.prompt = lyric
              const error = char.replace('[error]', '')
              if (error.indexOf('请登录') !== -1) {
                this.$emit('showLogin')
                setTimeout(() => {
                  this.$message.error(this.$lang(error))
                }, 500)
              } else if (error.indexOf('请充值') !== -1) {
                this.$emit('showPay', 'point')
                setTimeout(() => {
                  this.$message.error(this.$lang(error))
                }, 500)
              } else {
                this.$alert(error, '系统提示')
              }
              break
            }
            this.writing = true
            for (var i = 0; i < char.length; i++) {
              textStacks.push(char[i])
            }
          }
        }
        done = readerDone
      }
      this.writing = false
    },
    scrollLyricBottom() {
      this.$nextTick(() => {
        // const lyric = document.getElementById('lyric')
        // lyric.scrollTop = lyric.scrollHeight
      })
    },
    stopMakeLyric() {
      this.writing = false
      fetchCtrl.abort()
    }
  }
}
</script>
<style>
.box-music-setting .el-textarea textarea {
  padding: 8px 10px;
}
</style>
<style lang="scss" scoped>
.music-setting .module-body .box-music-setting {
  .uploader {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    border: 1px solid #DCDFE6;
    border-radius: 5px;
    overflow: hidden;
    width: 80px;
    height: 80px;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    .image {
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
    }
    .del {
      position: absolute;
      right: 0;
      top: 0;
      width: 18px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      font-size: 14px;
      background: rgba(0, 0, 0, 0.6);
      color: #fff;
      border-bottom-left-radius: 10px;
      &:hover {
        background: #000;
      }
    }
    .action {
      position: absolute;
      width: 100%;
      height: 24px;
      line-height: 24px;
      left: 0;
      bottom: 0;
      text-align: center;
      //background: rgb(249, 220, 171);
      background: linear-gradient(30deg, #f7debe, #f4c384);
      color: #9a5b12;
      font-size: 12px;
      border-radius: 2px;
    }
    .uploader-icon {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      color: #999;
      .icon {
        font-size: 24px;
      }
      p {
        margin: 0;
        font-size: 12px;
        text-align: center;
        line-height: 20px;
        margin-top: 5px;
      }
    }

    &:hover {
      border-color: #10a37f;
      .uploader-icon {
        color: #10a37f;
      }
    }

  }
  .size-item {
    width: 66px;
    height: 85px;
    border-radius: 5px;
    border-width: 1px;
  }
  .option-radio-group {
    .item {
      display: flex;
      align-items: center;
      float: left;
      margin: 5px 10px 5px 0;
      .label {
        display: inline-block;
        width: 36px;
        color: #666;
        font-size: 14px;
      }
    }
  }
}
</style>
