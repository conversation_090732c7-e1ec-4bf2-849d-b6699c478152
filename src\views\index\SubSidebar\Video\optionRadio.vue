<template>
  <div v-if="options && options.length > 0" class="option-radio">
    <div v-for="item in options" class="item" :class="{active: item.value === activeValue}" @click="onSelect(item.value)">
      <i v-if="item.icon" :class="item.icon" />
      <span v-if="item.title">{{ item.title }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      default() {
        return []
      }
    },
    name: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    canCancel: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      activeValue: ''
    }
  },
  watch: {
    value() {
      this.activeValue = this.value
    }
  },
  methods: {
    onSelect(value) {
      if (this.canCancel && this.activeValue === value) {
        value = ''
      }
      this.activeValue = value
      this.$emit('change', this.name, value)
    }
  }
}
</script>
<style lang="scss" scoped>
.option-radio {
  .item {
    float: left;
    margin-left: 10px;
    display: block;
    padding: 8px 24px;
    background: #fdfdfd;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    &.active {
      background: #10a37f;
      color: #fff;
    }
  }
}
</style>
