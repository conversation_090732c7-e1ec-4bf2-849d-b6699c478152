<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="box-video-setting">
      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '内容描述' | lang }}</span>
            <el-button type="primary" plain size="mini" title="所有参数恢复默认">重置</el-button>
          </div>
          <div class="options" style="position: relative;">
            <div class="upload">
              <div v-if="options.image || options.video" class="uploader">
                <a v-if="options.image" title="参考图" :href="options.image" target="_blank"><img class="image" :src="options.image"></a>
                <a v-if="options.video" title="参考视频" :href="options.video" target="_blank"><img class="image" src="/static/img/video.png"></a>
                <span class="del" title="移除" @click.stop="removeMedia">
                  <i class="el-icon-close" />
                </span>
                <div v-if="options.action === 'extend'" class="action"><i class="el-icon-time"></i> 延长4s</div>
                <div v-if="options.action === 'edit'" class="action"><i class="el-icon-edit-outline"></i> 编辑视频</div>
              </div>
              <el-upload
                v-else
                title="上传参考图片 / 视频"
                class="uploader"
                action=""
                accept=".jpg,.png,.mp4"
                :before-upload="checkMediaType"
                :http-request="uploadMedia"
                :show-file-list="false"
                :multiple="false"
              >
                <div class="uploader-icon">
                  <i class="icon el-icon-plus" />
                  <p>图片/视频</p>
                </div>
              </el-upload>
              <el-input
                ref="messageInput"
                v-model="options.prompt"
                :placeholder="'输入视频内容文字描述' | lang"
                type="textarea"
                :autofocus="true"
                :autosize="{ minRows: 3, maxRows: 8 }"
                maxlength="2000"
                style="padding-left: 90px;"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '视频比例' | lang }}</span>
            <el-tooltip effect="dark" content="指定生成图像的宽高比" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options" style="display: flex; justify-content: space-around;">
            <div v-for="item in sizes" class="size-item" :class="{active: options.aspectRatio === item.width + ':' + item.height}" @click="setSize(item.width, item.height)">
              <div v-if="item.width>=item.height" class="size-block" :style="'width: 36px; height: ' + parseInt((item.height/item.width) * 36) + 'px;'" />
              <div v-else class="size-block" :style="'height: 36px; width: ' + parseInt((item.width/item.height) * 36) + 'px;'" />
              <span>{{ item.title }}</span>
            </div>
            <div style="clear:both;" />
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '帧率' | lang }}</span>
            <el-tooltip effect="dark" :content="frameRate.explain" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <div style="padding-bottom: 10px;">
              <el-slider
                v-model="options.frameRate"
                :min="frameRate.min"
                :max="frameRate.max"
                :marks="frameRate.marks"
              />
            </div>
            <div style="clear:both;" />
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '镜头控制' | lang }}</span>
            <el-tooltip effect="dark" content="指摄影机位置不动,但摄影机方向会移动" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="option-radio-group">
            <div class="item">
              <span class="label">左右</span>
              <optionRadio :options="[{icon: 'el-icon-back', value: 'left'}, {icon: 'el-icon-right', value: 'right'}]" name="pan" :value="options.pan" :can-cancel="true" @change="optionChange" />
            </div>
            <div class="item" style="margin-right: 0; margin-left: 20px;">
              <span class="label">倾斜</span>
              <optionRadio :options="[{icon: 'el-icon-top', value: 'up'}, {icon: 'el-icon-bottom', value: 'down'}]" name="tilt" :value="options.tilt" :can-cancel="true" @change="optionChange" />
            </div>
            <div class="item">
              <span class="label">旋转</span>
              <optionRadio :options="[{icon: 'el-icon-refresh-left', value: 'ccw'}, {icon: 'el-icon-refresh-right', value: 'cw'}]" name="rotate" :value="options.rotate" :can-cancel="true" @change="optionChange" />
            </div>
            <div class="item" style="margin-right: 0; margin-left: 20px;">
              <span class="label">变焦</span>
              <optionRadio :options="[{icon: 'el-icon-zoom-in', value: 'in'}, {icon: 'el-icon-zoom-out', value: 'out'}]" name="zoom" :value="options.zoom" :can-cancel="true" @change="optionChange" />
            </div>
            <div style="clear:both;" />
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '与文本一致性' | lang }}</span>
            <el-tooltip effect="dark" :content="guidanceScale.explain" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <div style="padding-bottom: 10px;">
              <el-slider
                v-model="options.guidanceScale"
                :min="guidanceScale.min"
                :max="guidanceScale.max"
                :marks="guidanceScale.marks"
              />
            </div>
            <div style="clear:both;" />
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '运动增强' | lang }}</span>
            <el-tooltip effect="dark" :content="motion.explain" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <div>
              <el-slider
                v-model="options.motion"
                :min="motion.min"
                :max="motion.max"
                :marks="motion.marks"
              />
            </div>
            <div style="clear:both;" />
          </div>
        </div>
      </div>

      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '排除内容' | lang }}</span>
            <el-tooltip effect="dark" content="指定视频内要排除的生成内容" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-input v-model="options.negativePrompt" type="text" :placeholder="'请输入想从视频中排除的内容' | lang" size="normal" style="width: 420px;" />
            <div style="clear:both;" />
          </div>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-item">
          <div class="header">
            <span>✳ {{ '种子 --seed' | lang }}</span>
            <el-tooltip effect="dark" content="种子可以用于生成连续视频，使生成视频保持同一种风格" placement="bottom">
              <i class="el-icon-warning-outline tips" />
            </el-tooltip>
          </div>
          <div class="options">
            <el-input v-model="options.seed" type="text" :placeholder="'请输入seed种子编号' | lang" size="normal" style="width: 420px;" />
            <div style="clear:both;" />
          </div>
        </div>
      </div>

    </div>
  </el-scrollbar>
</template>

<script>
import { uploadMedia } from '@/api/video'
import optionRadio from './optionRadio'
export default {
  components: { optionRadio },
  data() {
    return {
      defaultOptions: {
        action: 'generate',
        water: 0,
        guidanceScale: 12,
        seed: '',
        negativePrompt: '',
        frameRate: 24,
        pan: '',
        rotate: '',
        tilt: '',
        zoom: '',
        motion: 0,
        prompt: '',
        aspectRatio: '16:9',
        image: '',
        video: ''
      },
      options: {},
      sizes: [
        {
          width: 16,
          height: 9,
          title: '16:9',
          desc: ''
        },
        {
          width: 9,
          height: 16,
          title: '9:16',
          desc: ''
        },
        {
          width: 1,
          height: 1,
          title: '1:1',
          desc: ''
        },
        {
          width: 5,
          height: 2,
          title: '5:2',
          desc: ''
        },
        {
          width: 4,
          height: 5,
          title: '4:5',
          desc: ''
        },
        {
          width: 4,
          height: 3,
          title: '4:3',
          desc: ''
        }
      ],
      frameRate: {
        min: 8,
        max: 24,
        marks: {
          8: '8',
          24: '24'
        },
        explain: '每秒帧数，越大越流畅，文件也越大'
      },
      motion: {
        min: 0,
        max: 4,
        marks: {
          0: '0',
          4: '4'
        },
        explain: '使生成的视频更具动感'
      },
      guidanceScale: {
        min: 5,
        max: 25,
        marks: {
          5: '5',
          12: '默认12',
          25: '25'
        },
        explain: '值越大，视频内容越遵守文字要求'
      },
      mediaType: ''
    }
  },
  created() {
    this.options = JSON.parse(JSON.stringify(this.defaultOptions))
  },
  methods: {
    optionChange(name, value) {
      this.$set(this.options, name, value)
    },
    getVideoOptions() {
      return JSON.parse(JSON.stringify(this.options))
    },
    clearVideoOptions() {
      this.options.image = ''
      this.options.video = ''
    },
    setVideoOptions(options) {
      options = Object.assign(this.defaultOptions, options)
      this.options = JSON.parse(JSON.stringify(options))
    },
    showPay(type) {
      this.$emit('showPay', type)
    },
    setSize(width, height) {
      this.options.aspectRatio = width + ':' + height
    },
    checkMediaType(field) {
      if (field.type !== 'image/png' && field.type !== 'image/jpeg' && field.type !== 'video/mp4') {
        this.$message.error(this.$lang('图片仅支持.png、.jpg 格式，视频仅支持 .mp4 格式'))
        return false
      }
      if (field.type === 'video/mp4' && field.size > 50 * 1024 * 1024) {
        this.$message.error('请上传小于50MB的视频')
      } else if (field.size > 20 * 1024 * 1024) {
        this.$message.error('请上传小于20MB的图片')
      }
      return true
    },
    uploadMedia(item) {
      var form = new FormData()
      form.append('file', item.file)
      if (item.data) {
        form.append('data', JSON.stringify(item.data))
      }
      uploadMedia(form).then(res => {
        this.mediaType = res.data.type
        if (res.data.type === 'image') {
          this.options.image = res.data.path
        } else if (res.data.type === 'video') {
          this.options.video = res.data.path
        }
      }).catch(res => {
        if (res.errno === 403) {
          this.$emit('showLogin')
        }
      })
    },
    removeMedia() {
      this.options.mediaType = ''
      this.options.image = ''
      this.options.video = ''
      this.options.action = 'generate'
    }
  }
}
</script>
<style>
.box-video-setting .el-textarea textarea {
  padding: 8px 10px;
}
</style>
<style lang="scss" scoped>
.video-setting .module-body .box-video-setting {
  .uploader {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    border-radius: 5px;
    overflow: hidden;
    width: 80px;
    height: 80px;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    .image {
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
    }
    .del {
      position: absolute;
      right: 0;
      top: 0;
      width: 18px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      font-size: 14px;
      background: rgba(0, 0, 0, 0.6);
      color: #fff;
      border-bottom-left-radius: 10px;
      &:hover {
        background: #000;
      }
    }
    .action {
      position: absolute;
      width: 100%;
      height: 24px;
      line-height: 24px;
      left: 0;
      bottom: 0;
      text-align: center;
      //background: rgb(249, 220, 171);
      background: linear-gradient(30deg, #f7debe, #f4c384);
      color: #9a5b12;
      font-size: 12px;
      border-radius: 2px;
    }
    .uploader-icon {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      color: #999;
      .icon {
        font-size: 24px;
      }
      p {
        margin: 0;
        font-size: 12px;
        text-align: center;
        line-height: 20px;
        margin-top: 5px;
      }
    }

    &:hover {
      border-color: #10a37f;
      .uploader-icon {
        color: #10a37f;
      }
    }

  }
  .size-item {
    width: 66px;
    height: 85px;
    border-radius: 5px;
    border-width: 1px;
  }
  .option-radio-group {
    .item {
      display: flex;
      align-items: center;
      float: left;
      margin: 5px 10px 5px 0;
      .label {
        display: inline-block;
        width: 36px;
        color: #666;
        font-size: 14px;
      }
    }
  }
}
</style>
