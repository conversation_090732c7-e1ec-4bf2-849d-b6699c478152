<template>
  <div>
    <!-- Sub-sidebar 部分 -->
    <div class="prompt-list-container">
      <div class="module-header">
        <div class="view-mode-switcher">
          <el-button-group>
            <el-button 
              :type="viewMode === 'browse' ? 'primary' : 'default'"
              size="small"
              @click="switchViewMode('browse')"
            >
              浏览模型
            </el-button>
            <el-button 
              :type="viewMode === 'favorites' ? 'primary' : 'default'"
              size="small"
              @click="switchViewMode('favorites')"
            >
              我的收藏
            </el-button>
          </el-button-group>
        </div>
        <div class="search">
          <el-input 
            v-model="searchKeyword" 
            :placeholder="searchPlaceholder" 
            prefix-icon="el-icon-search" 
            size="large" 
            :clearable="true" 
            autocomplete 
          />
        </div>
      </div>
      <div class="module-body" :style="{ top: viewMode === 'favorites' ? '110px' : '95px' }">
        <!-- 分类树 -->
        <el-scrollbar ref="categoryScroll" wrap-class="scrollbar-wrapper">
          <div class="category-list">
            <div
              v-for="category in categoryTree"
              :key="category.id"
              class="category-group"
            >
              <!-- 一级分类 -->
              <div
                class="category-item primary-category"
                :class="{ active: selectedCategoryId === category.id, expanded: category.expanded }"
                @click="toggleCategory(category)"
              >
                <div class="category-content">
                  <span class="category-title">{{ cleanCategoryTitle(category.title) }}</span>
                  <span class="category-count">({{ category.totalCount }})</span>
                </div>
                <div v-if="category.subCategories && category.subCategories.length > 0" class="expand-icon">
                  <i class="el-icon-arrow-right" :class="{ expanded: category.expanded }"></i>
                </div>
              </div>

              <!-- 二级分类 -->
              <div
                v-if="category.subCategories && category.subCategories.length > 0"
                class="sub-categories"
                :class="{ expanded: category.expanded }"
              >
                <div
                  v-for="subCategory in category.subCategories"
                  :key="subCategory.id"
                  class="category-item sub-category"
                  :class="{ active: selectedCategoryId === subCategory.id }"
                  @click="selectCategory(subCategory)"
                >
                  <span class="category-title">{{ cleanCategoryTitle(subCategory.title) }}</span>
                  <span class="category-count">({{ subCategory.prompts.length }})</span>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <!-- 右侧内容展示区域 - 独立区域 -->
    <div v-if="showContentArea" class="content-area-external" @click.self="closeContentArea">
      <div v-if="selectedCategory" class="content-header">
        <div class="header-left">
          <h3 class="selected-category-title">{{ cleanCategoryTitle(selectedCategory.title) }}</h3>
          <span class="prompt-count">共 {{ visiblePromptsCount }} 个模板</span>
        </div>
        <div class="header-right">
          <button class="close-btn" @click="closeContentArea" title="关闭">
            <i class="el-icon-close"></i>
          </button>
        </div>
      </div>
      <div v-else class="content-header">
        <div class="header-left">
          <h3 class="selected-category-title">请选择分类</h3>
          <span class="prompt-count">共 0 个模板</span>
        </div>
        <div class="header-right">
          <button class="close-btn" @click="closeContentArea" title="关闭">
            <i class="el-icon-close"></i>
          </button>
        </div>
      </div>

      <el-scrollbar ref="contentScroll" wrap-class="scrollbar-wrapper" @scroll="handleScroll">
        <div v-if="visiblePromptsCount > 0" class="prompt-grid">
          <div
            v-for="prompt in currentPrompts"
            :key="prompt.id"
            v-show="!prompt.hidden"
            class="prompt-item"
            :class="{ active: activePrompt && activePrompt.id === prompt.id }"
            @click="changePrompt(prompt)"
          >
            <div class="prompt-title" v-html="highlightKeyword(prompt.title, searchKeyword)"></div>
            <div class="prompt-desc">
              <span v-if="isHtmlContent(prompt.desc)" v-html="renderDescription(prompt.desc)"></span>
              <span v-else v-html="highlightKeyword(prompt.desc, searchKeyword)"></span>
            </div>
            <div class="prompt-ops">
              <div class="op-item" :title="'点击量' | lang">
                <svg-icon class="icon" icon-class="ic_view" />
                {{ prompt.views }}
              </div>
              <div class="op-item" :title="'使用量' | lang">
                <svg-icon class="icon" icon-class="ic_usage" />
                {{ prompt.usages }}
              </div>
              <div class="op-item" :title="'收藏' | lang" @click.stop="doPromptVote(prompt)">
                <svg-icon v-if="prompt.isVote === 1" class="icon" icon-class="ic_vote_active" />
                <svg-icon v-else class="icon" icon-class="ic_vote" />
              </div>
            </div>
          </div>
          
          <!-- 收藏列表加载更多 -->
          <div v-if="viewMode === 'favorites' && selectedCategory && selectedCategory.id === 'favorites_list'" class="load-more-section">
            <div v-if="favoritesLoading" class="loading-more">
              <i class="el-icon-loading"></i>
              <span>加载中...</span>
            </div>
            <div v-else-if="favoritePrompts.length < favoritesTotalCount" class="load-more-btn" @click="loadMoreFavorites">
              <span>加载更多收藏 ({{ favoritePrompts.length }}/{{ favoritesTotalCount }})</span>
            </div>
            <div v-else-if="favoritesTotalCount > 0" class="no-more-data">
              <span>已显示全部收藏</span>
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <div class="empty-icon">📝</div>
          <div class="empty-text">{{ getEmptyStateText() }}</div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { getAllPrompt, votePrompt, getPrompts, getVotePrompts } from '@/api/write'
import { promptAll } from '@/views/components'

export default {
  components: { promptAll },
  data() {
    return {
      topicList: [],
      categoryTree: [],
      selectedCategory: null,
      selectedCategoryId: null,
      activePrompt: null,
      searchKeyword: '',
      currentPrompts: [],
      showContentArea: false,
      // 搜索防抖相关
      searchTimer: null,
      isSearching: false,
      // 收藏列表管理相关
      viewMode: 'browse', // 'browse' | 'favorites'
      favoritePrompts: [],
      favoritesLoading: false,
      favoritesPage: 1,
      favoritesPageSize: 20,
      favoritesTotalCount: 0
    }
  },
  computed: {
    visiblePromptsCount() {
      if (!this.currentPrompts) return 0
      return this.currentPrompts.filter(p => !p.hidden).length
    },
    searchPlaceholder() {
      return this.viewMode === 'favorites' ? '在收藏中搜索' : '搜索模型'
    }
  },
  watch: {
    searchKeyword(newVal, oldVal) {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      
      // 设置300ms防抖
      this.searchTimer = setTimeout(() => {
        this.performSearch(newVal)
      }, 300)
    }
  },
  mounted() {
    this.getAllPrompt()
  },
  methods: {
    // 处理分类数据，将原始数据转换为分类树结构
    processCategories(topicList) {
      const categoryMap = new Map()
      const categoryTree = []

      // 遍历所有topic，按照分类规则进行分组
      topicList.forEach(topic => {
        const categoryId = this.getCategoryId(topic.title)
        const isSubCategory = this.isSubCategory(topic.title)

        if (isSubCategory) {
          // 二级分类处理
          const parentId = categoryId.split('-')[0]
          if (!categoryMap.has(parentId)) {
            // 如果父分类不存在，创建一个占位符
            categoryMap.set(parentId, {
              id: `placeholder_${parentId}`,
              title: `分类@${parentId}`,
              prompts: [],
              subCategories: [],
              totalCount: 0,
              expanded: false
            })
          }

          const subCategory = {
            id: topic.id,
            title: topic.title,
            prompts: topic.prompts || [],
            parentId: parentId
          }

          categoryMap.get(parentId).subCategories.push(subCategory)
          categoryMap.get(parentId).totalCount += subCategory.prompts.length
        } else {
          // 一级分类处理
          if (categoryMap.has(categoryId)) {
            // 如果已存在，更新标题和prompts，但保持原有的id
            const existingCategory = categoryMap.get(categoryId)
            existingCategory.title = topic.title
            existingCategory.prompts = topic.prompts || []
            existingCategory.totalCount = (topic.prompts ? topic.prompts.length : 0) + existingCategory.subCategories.reduce((sum, sub) => sum + sub.prompts.length, 0)
          } else {
            // 创建新的一级分类
            categoryMap.set(categoryId, {
              id: topic.id,
              title: topic.title,
              prompts: topic.prompts || [],
              subCategories: [],
              totalCount: topic.prompts ? topic.prompts.length : 0,
              expanded: false
            })
          }
        }
      })

      // 转换为数组并排序
      categoryMap.forEach(category => {
        categoryTree.push(category)
      })

      return categoryTree.sort((a, b) => {
        const aNum = parseInt(a.id) || 0
        const bNum = parseInt(b.id) || 0
        return aNum - bNum
      })
    },

    // 从title中提取分类ID
    getCategoryId(title) {
      const match = title.match(/@(\d+(?:-\d+)?)/)
      return match ? match[1] : '0'
    },

    // 判断是否为二级分类
    isSubCategory(title) {
      return title.includes('-') && title.includes('@')
    },

    // 切换分类展开/收起状态
    toggleCategory(category) {
      if (category.subCategories && category.subCategories.length > 0) {
        // 有子分类，切换展开状态
        this.$set(category, 'expanded', !category.expanded)

        // 如果是展开状态，同时选择这个分类显示其内容
        if (category.expanded) {
          this.selectCategory(category)
        }
      } else {
        // 没有子分类，直接选择分类
        this.selectCategory(category)
      }
    },

    // 选择分类
    selectCategory(category) {
      this.selectedCategory = category
      this.selectedCategoryId = category.id

      // 显示内容区域
      this.showContentArea = true

      // 获取当前分类下的所有prompts
      if (category.subCategories && category.subCategories.length > 0) {
        // 一级分类：包含自身prompts和所有子分类的prompts
        this.currentPrompts = [
          ...category.prompts,
          ...category.subCategories.reduce((acc, sub) => acc.concat(sub.prompts), [])
        ]
      } else {
        // 二级分类：只显示自身的prompts
        this.currentPrompts = category.prompts || []
      }

      // 确保所有prompts都有hidden属性，默认为false
      this.currentPrompts.forEach((prompt, index) => {
        if (prompt.hidden === undefined) {
          this.$set(prompt, 'hidden', false)
        }
      })

      // 应用搜索过滤
      this.filterPrompts()
    },

    // 过滤prompts - 优化搜索逻辑
    filterPrompts() {
      if (!this.currentPrompts) return

      const keyword = this.searchKeyword.trim().toLowerCase()
      if (!keyword) {
        // 没有搜索关键词，显示所有
        this.currentPrompts.forEach(prompt => {
          this.$set(prompt, 'hidden', false)
        })
      } else {
        // 有搜索关键词，进行过滤
        this.currentPrompts.forEach(prompt => {
          const titleMatch = prompt.title && prompt.title.toLowerCase().includes(keyword)
          const descMatch = prompt.desc && prompt.desc.toLowerCase().includes(keyword)
          // 支持多关键词搜索（空格分隔）
          const keywords = keyword.split(/\s+/).filter(k => k.length > 0)
          const multiKeywordMatch = keywords.every(k => {
            const titleContains = prompt.title && prompt.title.toLowerCase().includes(k)
            const descContains = prompt.desc && prompt.desc.toLowerCase().includes(k)
            return titleContains || descContains
          })
          
          this.$set(prompt, 'hidden', !titleMatch && !descMatch && !multiKeywordMatch)
        })
      }

      this.$forceUpdate()
    },

    // 检测内容是否包含HTML标签
    isHtmlContent(desc) {
      if (!desc) return false
      const htmlRegex = /<[^>]*>/
      return htmlRegex.test(desc)
    },

    // 渲染描述内容，支持HTML
    renderDescription(desc) {
      if (!desc) return ''
      // 进行基本的安全处理
      return this.sanitizeHtml(desc)
    },

    // 基本的HTML安全处理
    sanitizeHtml(html) {
      // 允许的标签列表
      const allowedTags = ['p', 'span', 'div', 'br', 'strong', 'b', 'em', 'i', 'u']

      // 简单的标签过滤（生产环境建议使用专业的HTML净化库）
      let sanitized = html.replace(/<script[^>]*>.*?<\/script>/gi, '')
      sanitized = sanitized.replace(/on\w+="[^"]*"/gi, '') // 移除事件处理器
      sanitized = sanitized.replace(/javascript:/gi, '') // 移除javascript协议

      return sanitized
    },

    // 高亮搜索关键词 - 支持多关键词高亮
    highlightKeyword(text, keyword) {
      if (!text || !keyword) return text
      
      const trimmedKeyword = keyword.trim()
      if (!trimmedKeyword) return text
      
      // 支持多关键词高亮（空格分隔）
      const keywords = trimmedKeyword.split(/\s+/).filter(k => k.length > 0)
      let highlightedText = text
      
      keywords.forEach(k => {
        const escapedKeyword = k.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
        const regex = new RegExp(`(${escapedKeyword})`, 'gi')
        highlightedText = highlightedText.replace(regex, '<mark class="search-highlight">$1</mark>')
      })
      
      return highlightedText
    },

    // 清理分类标题，移除@符号及其后面的内容
    cleanCategoryTitle(title) {
      if (!title) return ''
      // 移除@符号及其后面的所有内容
      return title.replace(/@.*$/, '').trim()
    },

    getAllPrompt() {
      getAllPrompt().then(res => {
        const topicList = res.data
        this.topicList = topicList

        // 处理分类数据
        this.categoryTree = this.processCategories(topicList)

        // 处理URL中的activePromptId
        const activePromptId = parseInt(this.$route.query.id)
        if (activePromptId) {
          this.findAndSelectPrompt(activePromptId)
          // 即使找到了prompt，也要显示模板选择面板
          this.$nextTick(() => {
            this.showContentArea = true
          })
        } else if (this.categoryTree.length > 0) {
          // 默认选择第一个分类并显示内容区域
          this.selectCategory(this.categoryTree[0])
          // 确保显示内容区域（必须在selectCategory之后设置）
          this.$nextTick(() => {
            this.showContentArea = true
          })
        }
      })
    },

    // 查找并选择指定的prompt
    findAndSelectPrompt(promptId) {
      for (const category of this.categoryTree) {
        // 在一级分类的prompts中查找
        const foundInPrimary = category.prompts.find(prompt => prompt.id === promptId)
        if (foundInPrimary) {
          this.selectCategory(category)
          this.activePrompt = foundInPrimary
          this.showContentArea = false  // 找到prompt后隐藏内容区域
          this.$emit('changePrompt', promptId)
          return
        }

        // 在二级分类的prompts中查找
        for (const subCategory of category.subCategories) {
          const foundInSub = subCategory.prompts.find(prompt => prompt.id === promptId)
          if (foundInSub) {
            this.selectCategory(subCategory)
            this.activePrompt = foundInSub
            this.showContentArea = false  // 找到prompt后隐藏内容区域
            this.$emit('changePrompt', promptId)
            return
          }
        }
      }
    },
    async doPromptVote(prompt) {
      // 乐观更新 - 立即更新UI
      const originalState = prompt.isVote
      const isAddingToFavorites = !prompt.isVote
      prompt.isVote = prompt.isVote ? 0 : 1
      this.$forceUpdate()
      
      try {
        const response = await votePrompt({
          prompt_id: prompt.id
        })
        
        // 显示成功消息
        this.$message.success(response.message || (prompt.isVote ? '收藏成功' : '已取消收藏'))
        
        // 更新收藏列表数据
        if (isAddingToFavorites) {
          // 添加到收藏列表
          this.addToFavorites(prompt)
        } else {
          // 从收藏列表中移除
          this.removeFromFavorites(prompt.id)
          
          // 如果当前在收藏模式且取消收藏，需要特殊处理
          if (this.viewMode === 'favorites' && this.selectedCategory && this.selectedCategory.id === 'favorites_list') {
            // 在收藏列表中取消收藏时，直接隐藏该项目
            this.$set(prompt, 'hidden', true)
            this.$message.info('已从收藏列表中移除')
          }
        }
        
      } catch (error) {
        // 操作失败，回滚状态
        prompt.isVote = originalState
        this.$forceUpdate()
        
        // 处理不同类型的错误
        this.handleVoteError(error)
      }
    },

    // 处理收藏操作错误
    handleVoteError(error) {
      if (error.errno === 403) {
        this.$message.warning('请先登录后再收藏')
        this.$emit('showLogin')
      } else if (error.errno === 401) {
        this.$message.warning('登录已过期，请重新登录')
        this.$emit('showLogin')
      } else {
        this.$message.error(error.message || '操作失败，请稍后重试')
      }
    },
    changePrompt(prompt) {
      this.activePrompt = prompt
      this.showContentArea = false  // 选择模型后隐藏内容区域
      this.$emit('switchModule', 'write', prompt.id)
    },

    getActiveId() {
      return this.activePrompt ? this.activePrompt.id : 0
    },

    // 获取空状态文本 - 优化不同场景的提示
    getEmptyStateText() {
      if (!this.selectedCategory) {
        return '请选择左侧分类查看模板'
      }
      
      // 收藏列表模式
      if (this.viewMode === 'favorites') {
        if (this.selectedCategory.id === 'favorites_list') {
          if (this.searchKeyword.trim()) {
            return `在收藏中未找到包含"${this.searchKeyword.trim()}"的模型`
          }
          return '您还没有收藏任何模型，去浏览模式收藏一些吧！'
        }
      }
      
      // 搜索结果分类
      if (this.selectedCategory.id === 'search_results') {
        const keyword = this.searchKeyword.trim()
        if (keyword) {
          return `未找到包含"${keyword}"的模型，请尝试其他关键词或检查拼写`
        }
        return '请输入关键词搜索模型'
      }
      
      // 普通分类
      if (!this.currentPrompts || this.currentPrompts.length === 0) {
        return '该分类下暂无模板'
      }
      
      // 有数据但被搜索过滤掉了
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.trim()
        return `在当前分类中未找到包含"${keyword}"的模板，请尝试其他关键词`
      }
      
      return '该分类下暂无可显示的模板'
    },

    // 关闭内容区域
    closeContentArea() {
      this.showContentArea = false
    },

    // 执行搜索 - 优化搜索逻辑和错误处理
    async performSearch(keyword) {
      const trimmedKeyword = keyword.trim()
      
      if (!trimmedKeyword) {
        // 如果搜索关键词为空，恢复到原始数据显示
        if (this.viewMode === 'browse') {
          this.filterPrompts()
        } else {
          // 收藏模式下清空搜索时显示所有收藏
          this.filterFavorites('')
        }
        return
      }

      // 如果是收藏模式，使用本地过滤
      if (this.viewMode === 'favorites') {
        this.filterFavorites(trimmedKeyword)
        return
      }

      this.isSearching = true
      
      try {
        const response = await getPrompts({
          keyword: trimmedKeyword,
          page: 1,
          pagesize: 100,
          topic_id: 'all'
        })
        
        // 处理搜索结果
        this.processSearchResults(response.data, trimmedKeyword)
      } catch (error) {
        this.handleSearchError(error, trimmedKeyword)
      } finally {
        this.isSearching = false
      }
    },

    // 处理搜索结果 - 优化结果处理逻辑
    processSearchResults(data, keyword) {
      if (data && data.list && data.list.length > 0) {
        // 创建一个虚拟的搜索结果分类
        const searchCategory = {
          id: 'search_results',
          title: `搜索结果 "${keyword}"`,
          prompts: data.list.map(prompt => ({
            ...prompt,
            hidden: false // 确保搜索结果都显示
          })),
          subCategories: [],
          totalCount: data.list.length,
          expanded: false
        }
        
        // 选择搜索结果分类
        this.selectCategory(searchCategory)
      } else {
        // 没有搜索结果
        this.currentPrompts = []
        this.selectedCategory = {
          id: 'search_results',
          title: `搜索结果 "${keyword}"`,
          prompts: [],
          subCategories: [],
          totalCount: 0
        }
        this.showContentArea = true
      }
    },

    // 处理搜索错误 - 增强错误处理
    handleSearchError(error, keyword) {
      console.error('搜索失败:', error)
      
      // 根据错误类型显示不同提示
      if (error.response) {
        const status = error.response.status
        if (status === 429) {
          this.$message.warning('搜索请求过于频繁，请稍后再试')
        } else if (status >= 500) {
          this.$message.error('服务器错误，请稍后重试')
        } else {
          this.$message.error('搜索失败，请检查网络连接')
        }
      } else if (error.code === 'NETWORK_ERROR' || !navigator.onLine) {
        this.$message.error('网络连接失败，请检查网络设置')
      } else {
        this.$message.error('搜索失败，请稍后重试')
      }
      
      // 搜索失败时回退到本地过滤
      this.filterPrompts()
    },

    // 收藏列表搜索过滤
    filterFavorites(keyword) {
      if (!this.favoritePrompts || this.favoritePrompts.length === 0) return
      
      const trimmedKeyword = keyword.trim().toLowerCase()
      
      if (!trimmedKeyword) {
        // 没有关键词，显示所有收藏
        this.favoritePrompts.forEach(prompt => {
          this.$set(prompt, 'hidden', false)
        })
      } else {
        // 有关键词，进行过滤
        this.favoritePrompts.forEach(prompt => {
          const titleMatch = prompt.title && prompt.title.toLowerCase().includes(trimmedKeyword)
          const descMatch = prompt.desc && prompt.desc.toLowerCase().includes(trimmedKeyword)
          
          // 支持多关键词搜索
          const keywords = trimmedKeyword.split(/\s+/).filter(k => k.length > 0)
          const multiKeywordMatch = keywords.every(k => {
            const titleContains = prompt.title && prompt.title.toLowerCase().includes(k)
            const descContains = prompt.desc && prompt.desc.toLowerCase().includes(k)
            return titleContains || descContains
          })
          
          this.$set(prompt, 'hidden', !titleMatch && !descMatch && !multiKeywordMatch)
        })
      }
      
      // 更新收藏分类的显示
      if (this.selectedCategory && this.selectedCategory.id === 'favorites_list') {
        this.selectedCategory.prompts = this.favoritePrompts
        this.$forceUpdate()
      }
    },

    // 切换视图模式
    switchViewMode(mode) {
      if (this.viewMode === mode) return
      
      this.viewMode = mode
      this.searchKeyword = '' // 清空搜索关键词
      
      if (mode === 'favorites') {
        // 切换到收藏模式
        this.loadFavoritesList()
      } else {
        // 切换到浏览模式
        this.showContentArea = false
        // 如果有分类数据，默认选择第一个分类
        if (this.categoryTree.length > 0) {
          this.selectCategory(this.categoryTree[0])
        }
      }
    },

    // 加载收藏列表 - 优化加载逻辑和用户体验
    async loadFavoritesList(page = 1, showLoading = true) {
      if (showLoading) {
        this.favoritesLoading = true
      }
      
      try {
        const response = await getVotePrompts({
          page: page,
          pagesize: this.favoritesPageSize
        })
        
        const newPrompts = response.data.list || []
        
        if (page === 1) {
          this.favoritePrompts = newPrompts.map(prompt => ({
            ...prompt,
            hidden: false // 确保收藏的模型都显示
          }))
        } else {
          // 分页加载时追加数据
          this.favoritePrompts.push(...newPrompts.map(prompt => ({
            ...prompt,
            hidden: false
          })))
        }
        
        this.favoritesTotalCount = response.data.count || 0
        this.favoritesPage = page
        
        // 创建收藏列表的虚拟分类
        const favoritesCategory = {
          id: 'favorites_list',
          title: `我的收藏 (${this.favoritesTotalCount})`,
          prompts: this.favoritePrompts,
          subCategories: [],
          totalCount: this.favoritesTotalCount,
          expanded: false
        }
        
        // 选择收藏列表分类
        this.selectCategory(favoritesCategory)
        
        // 如果有搜索关键词，应用过滤
        if (this.searchKeyword.trim()) {
          this.filterFavorites(this.searchKeyword)
        }
        
      } catch (error) {
        this.handleFavoritesError(error)
      } finally {
        if (showLoading) {
          this.favoritesLoading = false
        }
      }
    },

    // 处理收藏列表错误 - 增强错误处理
    handleFavoritesError(error) {
      console.error('获取收藏列表失败:', error)
      
      if (error.response) {
        const status = error.response.status
        if (status === 401 || status === 403) {
          this.$message.warning('请先登录后查看收藏')
          this.$emit('showLogin')
        } else if (status >= 500) {
          this.$message.error('服务器错误，请稍后重试')
        } else {
          this.$message.error('获取收藏列表失败，请检查网络连接')
        }
      } else if (error.errno === 403 || error.errno === 401) {
        this.$message.warning('请先登录后查看收藏')
        this.$emit('showLogin')
      } else {
        this.$message.error('获取收藏列表失败，请稍后重试')
      }
    },

    // 加载更多收藏 - 分页加载功能
    async loadMoreFavorites() {
      if (this.favoritesLoading) return
      
      const hasMore = this.favoritePrompts.length < this.favoritesTotalCount
      if (!hasMore) return
      
      const nextPage = this.favoritesPage + 1
      await this.loadFavoritesList(nextPage, false)
    },

    // 从收藏列表中移除项目
    removeFromFavorites(promptId) {
      const index = this.favoritePrompts.findIndex(p => p.id === promptId)
      if (index > -1) {
        this.favoritePrompts.splice(index, 1)
        this.favoritesTotalCount = Math.max(0, this.favoritesTotalCount - 1)
        
        // 更新收藏分类的数据
        if (this.selectedCategory && this.selectedCategory.id === 'favorites_list') {
          this.selectedCategory.prompts = this.favoritePrompts
          this.selectedCategory.totalCount = this.favoritesTotalCount
          this.selectedCategory.title = `我的收藏 (${this.favoritesTotalCount})`
          this.$forceUpdate()
        }
      }
    },

    // 添加到收藏列表
    addToFavorites(prompt) {
      // 检查是否已存在
      const exists = this.favoritePrompts.find(p => p.id === prompt.id)
      if (!exists) {
        this.favoritePrompts.unshift({
          ...prompt,
          hidden: false,
          isVote: 1
        })
        this.favoritesTotalCount += 1
        
        // 更新收藏分类的数据
        if (this.selectedCategory && this.selectedCategory.id === 'favorites_list') {
          this.selectedCategory.prompts = this.favoritePrompts
          this.selectedCategory.totalCount = this.favoritesTotalCount
          this.selectedCategory.title = `我的收藏 (${this.favoritesTotalCount})`
          this.$forceUpdate()
        }
      }
    },

    // 处理滚动事件 - 自动加载更多收藏
    handleScroll(e) {
      if (this.viewMode !== 'favorites' || !this.selectedCategory || this.selectedCategory.id !== 'favorites_list') {
        return
      }
      
      if (this.favoritesLoading || this.favoritePrompts.length >= this.favoritesTotalCount) {
        return
      }
      
      const { scrollTop, scrollHeight, clientHeight } = e.target
      // 当滚动到距离底部100px时自动加载更多
      if (scrollTop + clientHeight >= scrollHeight - 100) {
        this.loadMoreFavorites()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-highlight {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

.content-area-external {
  /* 确保不会遮挡Element UI的Loading组件 */
  z-index: 1000;
}

.prompt-list-container {
  .module-header {
    padding: 15px;
    background: #f7f7f8;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
    z-index: 10;
    
    .view-mode-switcher {
      margin-bottom: 12px;
      
      .el-button-group {
        width: 100%;
        
        .el-button {
          flex: 1;
          font-size: 12px;
        }
      }
    }
    
    .search {
      .el-input {
        .el-input__inner {
          border-radius: 6px;
          border: 1px solid #dcdfe6;
          
          &:focus {
            border-color: #10a37f;
          }
        }
      }
    }
  }
  
  .module-body {
    background: #f7f7f8;
    position: relative;
    z-index: 1;
  }
}

.load-more-section {
  padding: 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  margin-top: 20px;
  
  .loading-more {
    color: #999;
    font-size: 14px;
    
    i {
      margin-right: 8px;
    }
  }
  
  .load-more-btn {
    color: #409eff;
    cursor: pointer;
    font-size: 14px;
    padding: 8px 16px;
    border: 1px solid #409eff;
    border-radius: 4px;
    transition: all 0.3s;
    display: inline-block;
    
    &:hover {
      background-color: #409eff;
      color: white;
    }
  }
  
  .no-more-data {
    color: #999;
    font-size: 12px;
  }
}
</style>
